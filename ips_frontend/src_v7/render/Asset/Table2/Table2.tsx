import { AssetLogic } from '@v7_logic/AssetLogic';
import { AssetTableLogic } from '@v7_logic/AssetTableLogic/AssetTableLogic';
import { AssetDragLogic } from '@v7_logic/AssetDragLogic/AssetDragLogic';
import { IAsset, ICanvas, ICell, IText } from '@v7_logic/Interface';
import React, { PureComponent } from 'react';
import './scss/Table2.scss';
import { calcSelectedTds, onTableControllerLineMouseDown, swapCol, swapRow } from './utils';
import {
    BottomAddRow,
    CellController,
    MoveController,
    RightAddCol,
    RowAndColLines,
    RowAndColSelect,
    SelectedTds,
    SelectColAndRowDrag,
    ShowRestDragLine
} from './TableUi';
import {emitter} from '@component/Emitter';

// 待替换组件
import { getFontNameValueList } from '@component/IPSConfig';
import { assetManager } from '@src/userComponentV6.0/AssetManager';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { message } from 'antd';

const fontNameValueList = getFontNameValueList();

// 事件管理器
class EventManager {
    private listeners: Map<string, { element: any; event: string; handler: any }> = new Map();

    add(key: string, element: any, event: string, handler: any) {
        this.remove(key); // 先移除旧的监听器
        element.addEventListener(event, handler);
        this.listeners.set(key, { element, event, handler });
    }

    remove(key: string) {
        const listener = this.listeners.get(key);
        if (listener) {
            listener.element.removeEventListener(listener.event, listener.handler);
            this.listeners.delete(key);
        }
    }

    removeAll() {
        this.listeners.forEach((listener, key) => {
            listener.element.removeEventListener(listener.event, listener.handler);
        });
        this.listeners.clear();
    }
}

const defaultFunction = () => {
    /* do nothing */
};

const onTdContentPaste = (e: React.ClipboardEvent<HTMLElement>) => {
    let isString= true, isOverLimit = false;
    try{
        const data = e.clipboardData?.getData('text')
        isString = !!(data && typeof JSON.parse(data) != 'object')
        isOverLimit = data?.length > 2000
    }catch(e){
        // console.error(e)
        isString = true
        isOverLimit = false
    }
    if(!isString || isOverLimit) {
        message.error('仅支持纯文本粘贴，长度限制2000以内')
        e.preventDefault()
        e.stopPropagation();
        e.nativeEvent?.stopPropagation();
        return 
    }
    e.stopPropagation();
    e.nativeEvent?.stopPropagation();
};

const windowInfo = {
    windowContent: <div style={{ textAlign: 'center', lineHeight: '70px' }}>进行此操作，请先拆分单元格！</div>,
}

interface IAssetTable2Props {
    assetProps?: {
        showOnly?: boolean;
        assetClassName?: string;
        canvasScale?: number;
    };
    asset?: IAsset;
    canvas?: ICanvas;
    isShowOnly?: boolean;
    isMoving?: boolean;
    index?: number;
    isActive: boolean;
    hideDragTable?: boolean;
}

export interface IAssetTable2State {
    isLoading?: boolean;
    choosedTdKeys?: string[];
    selectSize?: {
        startRow: number;
        startCol: number;
        endRow: number;
        endCol: number;
    };
    editingCell: string;
    activeControllerLine: string;
    dragWidth: number | undefined;
    dragHeight: number | undefined;
    x: string | undefined;
    y: string | undefined;
    isDragColAndRow: boolean;
    tableControllerDomSite: any;
    lineSite: any, // 线段位置坐标
    nearLineCol: number; // 距离移动位置最近的一根线位置
    nearLineRow: number; 
    startDragLine: boolean;
    addIconIndex: string; //处理选中行列div悬停时两边的添加行列的加号icon
    hoverBgcIndex: string; //处理悬停选中行列div的悬停背景
    // editorState?: EditorState;
    // tdClickState?: number;
    // isBlured?: boolean;
    // hiddenShowTextStr?: IAnyObj;
    // currentEditTdPos?: string;
    // changeCellHeightOptions: IAnyObj;
    // cellPrevHeight: IAnyObj;
}

export class AssetTable2 extends PureComponent<IAssetTable2Props, IAssetTable2State> {
    baseRow = 0;
    baseCol = 0;
    colWidth: number[];
    rowHeight: number[];
    mouseDownX = 0;
    mouseDownY = 0;
    mouseDownCell: string = undefined;
    tableControllerDom = React.createRef<HTMLDivElement>();
    tableDom = React.createRef<HTMLTableElement>();
    haveSelectedKeylistener = false;
    
    // 拖动表格相关属性
    tableDragStartX = 0;
    tableDragStartY = 0;
    tableDragStartPosX = 0;
    tableDragStartPosY = 0;
    tableDragStarted = false;
    
    // 添加缓存相关属性
    private calcCache: {
        cellSize?: any;
        width?: number;
        height?: number;
        baseRow?: number;
        baseCol?: number;
        rowHeight?: number[];
        colWidth?: number[];
    } = {};
    
    // 事件管理器
    private eventManager = new EventManager();
    
    lastInputInfo: {
        row: number;
        col: number;
        value: string[];
        assetClassName: string;
        assetIndex: number;
        pageIndex: number;
    } = {
        row: -1,
        col: -1,
        value: [],
        assetClassName: undefined,
        assetIndex: -1,
        pageIndex: -1,
    }
    pageIndex = -1;

    onTableControllerLineMouseDown = (e: React.MouseEvent<HTMLElement>) => {
        // 行列边框操作不受表格激活状态限制
        onTableControllerLineMouseDown(e, this.setState.bind(this), this.getMinHeightFromSelection);
    };

    selectRowAndCol: {
        type: 'row' | 'col';
        row: number;
        col: number;
    } = {
        type: undefined,
        row: undefined,
        col: undefined,
    };

    state: IAssetTable2State = {
        choosedTdKeys: [],
        selectSize: undefined,
        editingCell: undefined,
        activeControllerLine: undefined,
        dragWidth: undefined,
        dragHeight: undefined,
        x: undefined,
        y: undefined,
        isDragColAndRow: false,
        tableControllerDomSite: {},
        lineSite: {},
        nearLineRow: undefined,
        nearLineCol: undefined,
        startDragLine: false,
        addIconIndex: undefined,
        hoverBgcIndex: undefined,
    };

    DragflagColAndRow = true;
    // 记录将要拖动时按下的位置
    ClientDragPosition = 0;
    // 判断移动的方向
    mouseL = false;
    mouseR = false;
    mouseD = false;
    mouseT = false;
    

    /** 计算行高列宽 */
    calcTotalRowAndCol(cellSize: IAsset['attribute']['cellSize'], width: number, height: number) {
        // 检查缓存是否有效
        if (
            this.calcCache.cellSize === cellSize &&
            this.calcCache.width === width &&
            this.calcCache.height === height &&
            this.calcCache.rowHeight &&
            this.calcCache.colWidth
        ) {
            // 使用缓存的结果
            this.baseRow = this.calcCache.baseRow!;
            this.baseCol = this.calcCache.baseCol!;
            this.rowHeight = this.calcCache.rowHeight;
            this.colWidth = this.calcCache.colWidth;
            return;
        }

        // 计算新的值
        const totalRow = cellSize.row.reduce((a, b) => (a ?? 30) + (b ?? 30), 0);
        const totalCol = cellSize.col.reduce((a, b) => (a ?? 30) + (b ?? 30), 0);
        this.baseRow = height / totalRow;
        this.baseCol = width / totalCol;
        this.rowHeight = cellSize.row.map((r) => r * this.baseRow);
        this.colWidth = cellSize.col.map((c) => c * this.baseCol);

        // 更新缓存
        this.calcCache = {
            cellSize,
            width,
            height,
            baseRow: this.baseRow,
            baseCol: this.baseCol,
            rowHeight: [...this.rowHeight],
            colWidth: [...this.colWidth],
        };
    }

    /** 选择单元格 - 按下 */
    onCellControllerMouseDown = (e: React.MouseEvent<HTMLElement>) => {
        if (this.state.choosedTdKeys?.length > 0) {
            e.stopPropagation();
        }
        if (!this.props.isActive) {
            return;
        }
        const { cell } = e.currentTarget.dataset;
        this.mouseDownX = e.clientX;
        this.mouseDownY = e.clientY;
        this.mouseDownCell = cell;

        this.eventManager.add('cellMove', window, 'mousemove', this.onCellControllerMouseMove);
        this.eventManager.add('cellUp', window, 'mouseup', this.onCellControllerMouseUp);
    };

    /** 选择单元格 - 弹起 */
    onCellControllerMouseUp = (e: MouseEvent) => {
        if (this.state.choosedTdKeys?.length > 0) {
            e.stopPropagation();
        }
        if (!this.props.isActive) {
            return;
        }
        
        const { cell } = (e.target as HTMLElement).dataset;
        
        // 检测是否为单击（而非拖拽）- 鼠标移动距离小于5像素
        const mouseMoveDistance = Math.sqrt(
            Math.pow(e.clientX - this.mouseDownX, 2) + 
            Math.pow(e.clientY - this.mouseDownY, 2)
        );
        const isClick = mouseMoveDistance < 5;
        
        if (cell && !this.props.isMoving && this.mouseDownCell) {
            if (isClick) {
                // 单击事件
                if (this.state.choosedTdKeys?.length === 1 && this.state.choosedTdKeys[0] === cell) {
                    // 如果是单击，并且单击的单元格是当前唯一选中的单元格，则进入编辑模式
                    this.enterEditMode(cell);
                } else {
                    // 否则，执行单选
                    const { selectedTd, size } = calcSelectedTds(this.props.asset.attribute.cell, cell, cell);
                    this.setState({
                        choosedTdKeys: selectedTd,
                        selectSize: size,
                    });
                    AssetLogic.updateTableSelectKey({
                        tdKeys: selectedTd,
                        className: this.props.asset.meta.className,
                    });
                }
            } 
            // else {
            //     // 拖拽选择
            //     const { selectedTd, size } = calcSelectedTds(this.props.asset.attribute.cell, this.mouseDownCell, cell);
            //     this.setState({
            //         choosedTdKeys: selectedTd,
            //         selectSize: size,
            //     });
            //     AssetLogic.updateTableSelectKey({
            //         tdKeys: selectedTd,
            //         className: this.props.asset.meta.className,
            //     });
            // }
        }
        
        this.mouseDownX = 0;
        this.mouseDownY = 0;
        this.mouseDownCell = undefined;
        this.eventManager.remove('cellMove');
        this.eventManager.remove('cellUp');
    };

    /** 选择单元格 - 移动 */
    onCellControllerMouseMove = (e: MouseEvent) => {
        if (this.state.choosedTdKeys?.length > 0) {
            e.stopPropagation();
        }
        if (!this.props.isActive) {
            return;
        }
        if (this.tableControllerDom?.current?.contains(e.target as HTMLElement)) {
            const { cell } = (e.target as HTMLElement).dataset;
            if (cell && !this.props.isMoving && this.state.choosedTdKeys?.length > 0 && this.mouseDownCell) {
                const { selectedTd, size } = calcSelectedTds(this.props.asset.attribute.cell, this.mouseDownCell, cell);
                this.setState({
                    choosedTdKeys: selectedTd,
                    selectSize: size,
                });
            }
        }
    };

    /** 进入单元格编辑状态的通用方法 */
    enterEditMode = (cell: string) => {
        const [row, col] = cell.split('_');
        this.setState(
            {
                editingCell: cell,
            },
            () => {
                this.tableDom?.current?.querySelector<HTMLElement>(`.table-td-content-${row}-${col}`)?.focus();
            },
        );
    };

    /** 选择单元格 - 双击编辑 */
    onCellControllerDoubleClick = (e: React.MouseEvent<HTMLElement>) => {
        const { cell } = e.currentTarget.dataset;
        if (cell) {
            this.enterEditMode(cell);
        }
    };

    /** 单元格内容编辑 - 同步输入内容，防止失焦时直接销毁表格导致没有保存成功 */
    onInput = (e: React.FormEvent<HTMLElement>) => {
        const { row, col } = e.currentTarget.dataset;
        const text = e.currentTarget.innerHTML.replace(/<br>/g, '\n')?.replace(/&amp;/g, '&');
        
        const textArr = text.split('\n');
        if (this.props.asset?.meta?.className) {
            if (this.pageIndex < 0) {
                const { pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });
                this.pageIndex = pageInfo.pageNow;
            }
            this.lastInputInfo = {
                row: Number.parseInt(row),
                col: Number.parseInt(col),
                value: textArr,
                assetClassName: this.props.asset.meta.className,
                assetIndex: this.props.index,
                pageIndex: this.pageIndex,
            }
        }
    }

    onTdContentFocus = (e: React.FocusEvent<HTMLElement>) => {
        // 全选内容
        const target = e.currentTarget;
        
        // 使用 setTimeout 确保在焦点设置后再执行全选
        setTimeout(() => {
            try {
                const selection = window.getSelection();
                const range = document.createRange();
                
                // 选择元素内的所有内容
                range.selectNodeContents(target);
                
                // 清除之前的选择并设置新的选择
                selection?.removeAllRanges();
                selection?.addRange(range);
            } catch (error) {
                console.warn('全选文字失败:', error);
            }
        }, 0);
        
        e.stopPropagation();
        e.nativeEvent?.stopPropagation();
    }

    /** 单元格内容编辑 - 失去焦点保存 */
    onBlur = (e: React.FormEvent<HTMLElement>) => {
        const { row, col } = e.currentTarget.dataset;
        this.setState({
            editingCell: undefined,
        });
        const text = e.currentTarget.innerHTML.replace(/<br>/g, '\n').replace(/&amp;/g, '&').replace(/&nbsp;/g, ' ').replace(/&lt;/g, '<').replace(/&gt;/g, '>');

        const textArr = text.split('\n');
        if (this.props.asset?.meta?.className) {
            AssetLogic.updateTableText({
                row: Number.parseInt(row),
                col: Number.parseInt(col),
                value: textArr,
                assetClassName: this.props.asset.meta.className,
                assetIndex: this.props.index,
            });
        }
        window.getSelection()?.removeAllRanges();
        this.lastInputInfo = {
            row: -1,
            col: -1,
            value: [],
            assetClassName: undefined,
            assetIndex: -1,
            pageIndex: -1,
        }
    };

    /** 单元格内容编辑 - 输入按下 */
    onTdContentKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
        // 输入状态下按下Tab键
        if (e.key === 'Tab') {
            e.preventDefault();
            const { row, col, cell } = e.currentTarget.dataset;
            const { asset } = this.props;
            const cellDate = asset.attribute.cell;
            const colEndIndex = cellDate[0].length-1;
            const rowEndIndex = cellDate.length - 1;
            let colIndex = Number(col);
            let rowIndex = Number(row);
            // 不是表格最后一个
            if (cell !== `${rowEndIndex}_${colEndIndex}`) {
                // 行最后一个，换行
                if (colIndex === colEndIndex) {
                    rowIndex += 1;
                    colIndex = 0;
                } else {
                    colIndex += 1;
                }
            }
            this.setState(
                {
                    editingCell: `${rowIndex}_${colIndex}`,
                },
                () => {
                    this.tableDom?.current?.querySelector<HTMLElement>(`.table-td-content-${rowIndex}-${colIndex}`)?.focus();
                },
            );
            this.setState({
                choosedTdKeys: [`${rowIndex}_${colIndex}`],
                selectSize: {
                    "startRow": rowIndex,
                    "startCol":colIndex,
                    "endRow": rowIndex,
                    "endCol": colIndex,
                },
            });
            AssetLogic.updateTableSelectKey({
                tdKeys: [`${rowIndex}_${colIndex}`],
                className: asset.meta.className,
            });
        }
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
    };

    /** 单元格内容编辑 - 输入弹起 */
    onTdContentKeyUp = (e: React.KeyboardEvent<HTMLDivElement>) => {
        e.stopPropagation();
        e.nativeEvent.stopPropagation();
        // const text = e.currentTarget.innerHTML;
        // console.log(text);
    };

    onTableMouseDown = (e: React.MouseEvent<HTMLElement>) => {
        // 不要阻止事件传播，让其他事件处理器也能工作
        // e.stopPropagation();
        // e.nativeEvent?.stopPropagation();

        console.log('Table mouse down:', {
            choosedTdKeys: this.state.choosedTdKeys,
            isActive: this.props.isActive,
            isMoving: this.props.isMoving,
            target: e.target
        });

        // 如果没有选中的单元格（显示 move 光标状态），则准备表格拖动
        if (!this.state.choosedTdKeys?.length && this.props.isActive && !this.props.isMoving) {
            const { asset } = this.props;
            if (!asset) return;

            console.log('Preparing table drag...');

            // 记录拖动开始的位置
            this.tableDragStartX = e.clientX;
            this.tableDragStartY = e.clientY;
            this.tableDragStartPosX = asset.transform.posX;
            this.tableDragStartPosY = asset.transform.posY;

            // 添加鼠标移动和弹起事件监听，但不立即开始拖动
            this.eventManager.add('tableDragMove', window, 'mousemove', this.onTableMoveMouseMove);
            this.eventManager.add('tableDragUp', window, 'mouseup', this.onTableMoveMouseUp);
        }
    };

    /** 行列选择 - 按下 */
    onRowAndColSelectorMouseDown = (e: React.MouseEvent<HTMLElement>) => {
        this.setState({ hoverBgcIndex: ''});
        e.stopPropagation();
        assetManager.setPv_new(7063);
        const { row, col, type } = e.currentTarget.dataset;
        this.selectRowAndCol = {
            type: type as 'row' | 'col',
            row: row ? Number.parseInt(row) : undefined,
            col: col ? Number.parseInt(col) : undefined,
        };
        const className = (e.target as HTMLElement).className
        const active = className.includes('full') && className.includes('active');

        const { asset } = this.props;
        const { selectSize } = this.state;

        const cell = asset.attribute.cell;
        const mergedArr:any = []
        cell.forEach((item:any, index: number) => {
            const filterArr = item.filter((item:any, index:number) => {
                return Object.keys(item).includes('merged')
            })
            if (filterArr.length > 0) {
                mergedArr.push(filterArr)
            }
        })
        const flatArr = mergedArr.flat()
        if (flatArr.length > 0) {
            this.DragflagColAndRow = false;
        }

        if (active && this.selectRowAndCol.type === 'col') {
            const dragOneCol = selectSize.endCol - selectSize.startCol + 1;
            const m = [];
            
            if (asset.attribute.cellSize.col.length === dragOneCol || dragOneCol > 1) {
                return
            } else {
                assetManager.setPv_new(7064);
                this.ClientDragPosition = e.clientX;
                const ele: any = document.getElementsByClassName('assetEditBox')[0];
                const outermostCanvasEle:any = document.getElementsByClassName('paintCanvaStyle')[0];
                if (ele) {
                    ele.style.visibility = 'hidden'
                }
                outermostCanvasEle.style.cursor = 'move';
                (this.tableControllerDom?.current as HTMLElement).style.cursor = 'move';
                const width = this.colWidth[selectSize.endCol]
                const { canvas } = this.props;
                const scale = this.props.assetProps?.canvasScale ?? canvas?.scale ?? 1;
                const tableControllerDomSite = (this.tableControllerDom?.current as HTMLElement)?.getBoundingClientRect();
                const newArr = [];

                for(let i = 0; i <= this.colWidth.length; i++) {
                    if (i === 0) {
                        newArr.push(tableControllerDomSite.left)
                    } else {
                        let lineDistance = 0;
                        for(let j = 0; j < i; j++) {
                            lineDistance += this.colWidth[j] * scale
                        }
                        newArr.push(tableControllerDomSite.left + lineDistance)
                    }
                }
                for(let i = 0; i < newArr.length - 1; i++) {
                    const temp = [];
                    temp[0] = newArr[i];
                    temp[1] = newArr[i + 1];
                    m.push(temp)
                }
                
                this.setState({
                    dragWidth: width,
                    isDragColAndRow: true,
                    tableControllerDomSite: tableControllerDomSite,
                    lineSite: m,
                    nearLineCol: selectSize.endCol
                })
                this.eventManager.add('colDragMove', window, 'mousemove', this.onColDragMove);
                this.eventManager.add('colDragUp', window, 'mouseup', this.onColDragMouseUp);
            }
        } else if (active && this.selectRowAndCol.type === 'row') {
            assetManager.setPv_new(7064);
            this.ClientDragPosition = e.clientY
            const ele: any = document.getElementsByClassName('assetEditBox')[0];
            const outermostCanvasEle:any = document.getElementsByClassName('paintCanvaStyle')[0];
            if (ele) {
                ele.style.visibility = 'hidden'
            }
            outermostCanvasEle.style.cursor = 'move';
            (this.tableControllerDom?.current as HTMLElement).style.cursor = 'move';
            const dragOneRow = selectSize.endRow - selectSize.startRow + 1;
            const m = [];
            if (asset.attribute.cellSize.row.length === dragOneRow || dragOneRow > 1) {
                return
            } else {
                const height = this.rowHeight[selectSize.endRow]
                const { canvas } = this.props;
                const scale = this.props.assetProps?.canvasScale ?? canvas?.scale ?? 1;
                const tableControllerDomSite = (this.tableControllerDom?.current as HTMLElement)?.getBoundingClientRect();
                const newArr = [];
                for(let i = 0; i <= this.rowHeight.length; i++) {
                    if (i === 0) {
                        newArr.push(tableControllerDomSite.top)
                    } else {
                        let lineDistance = 0;
                        for(let j = 0; j < i; j++) {
                            lineDistance += this.rowHeight[j] * scale
                        }
                        newArr.push(tableControllerDomSite.top + lineDistance)
                    }
                }
                for(let i = 0; i < newArr.length - 1; i++) {
                    const temp = [];
                    temp[0] = newArr[i];
                    temp[1] = newArr[i + 1];
                    m.push(temp)
                }

                this.setState({
                    dragHeight: height,
                    isDragColAndRow: true,
                    tableControllerDomSite: tableControllerDomSite,
                    lineSite: m,
                    nearLineRow: selectSize.endRow
                })

                this.eventManager.add('rowDragMove', window, 'mousemove', this.onRowDragMove);
                this.eventManager.add('rowDragUp', window, 'mouseup', this.onRowDragMouseUp);
            }
        } else {
            this.eventManager.add('rowColMove', window, 'mousemove', this.onRowAndColSelectorMouseMove);
            this.eventManager.add('rowColUp', window, 'mouseup', this.onRowAndColSelectorMouseUp);
        }
    };

    // 拖拽列移动事件
    onColDragMove = (e: MouseEvent) => {
        e.stopPropagation();
        const { canvas, asset } = this.props;
        if (!this.DragflagColAndRow) {
            emitter.emit('PromptBox', windowInfo);
            window.removeEventListener('mousemove', this.onColDragMove);
            setTimeout(() => {
                emitter.emit('PromptBoxClose');
            }, 2000);
            return
        }
        if (e.clientX > this.ClientDragPosition) {
            this.mouseR = true;
            this.mouseL = false;
        }
        if (e.clientX < this.ClientDragPosition) {
            this.mouseL = true;
            this.mouseR = false;
        }
        if (this.DragflagColAndRow) {
            const scale = this.props.assetProps?.canvasScale ?? canvas?.scale ?? 1;
            const { tableControllerDomSite, dragWidth, lineSite } = this.state;
            const x = (e.clientX - tableControllerDomSite.left - 1) / scale - dragWidth / 2;

            const filterArr = lineSite.filter((item: any, index: number) => {
                return e.clientX > item[0] && e.clientX < item[1]
            })
            const nearLineCol = lineSite.indexOf(filterArr[0])
            if (x <= 0) {
                this.setState({
                    x: '0',
                    nearLineCol: 0,
                    startDragLine: true
                })
            } else if (x >= this.tableControllerDom?.current.offsetWidth - dragWidth) {
                this.setState({
                    x: (this.tableControllerDom?.current.offsetWidth - dragWidth).toString(),
                    nearLineCol: this.colWidth.length,
                    startDragLine: true
                })
            } else {
                this.setState({
                    x: x.toString(),
                    nearLineCol: this.mouseR ? nearLineCol + 1 : nearLineCol,
                    startDragLine: true,
                })
            }
        }
    }

    // 拖拽列MouseUp
    onColDragMouseUp = (e: MouseEvent) => {
        e.stopPropagation();
        const { nearLineCol, selectSize } = this.state;
        const ele: any = document.getElementsByClassName('assetEditBox')[0];
        if (ele) {
            ele.style.visibility = 'visible'
        }
        const outermostCanvasEle:any = document.getElementsByClassName('paintCanvaStyle')[0];
        outermostCanvasEle.style.cursor = 'auto';
        (this.tableControllerDom?.current as HTMLElement).style.cursor = '';
        const nearLine = this.mouseR ? nearLineCol - 1 : nearLineCol
        swapCol(nearLine, selectSize.endCol)
        const rowHeightLength = this.rowHeight.length;
        this.setState({
            isDragColAndRow: false,
            startDragLine: false,
            x: undefined,
            selectSize: {
                startRow: 0,
                endRow: rowHeightLength - 1,
                startCol: nearLine,
                endCol: nearLine,
            }

        })
        this.DragflagColAndRow = true;
        this.ClientDragPosition = 0;
        this.mouseR = false;
        this.mouseL = false;
        this.eventManager.remove('colDragMove');
        this.eventManager.remove('colDragUp');
    }

    // 拖拽行移动事件
    onRowDragMove = (e: MouseEvent) => {
        e.stopPropagation();
        const { canvas } = this.props;
        if (!this.DragflagColAndRow) {
            emitter.emit('PromptBox', windowInfo);
            window.removeEventListener('mousemove', this.onColDragMove);
            setTimeout(() => {
                emitter.emit('PromptBoxClose');
            }, 2000);
            return
        }
        if (e.clientY > this.ClientDragPosition) {
            this.mouseD = true;
            this.mouseT = false;
        }
        if (e.clientY < this.ClientDragPosition) {
            this.mouseT = true;
            this.mouseD = false;
        }
        if (this.DragflagColAndRow) {
            const scale = this.props.assetProps?.canvasScale ?? canvas?.scale ?? 1;
            const { tableControllerDomSite, dragHeight, lineSite } = this.state;
            const y = (e.clientY - tableControllerDomSite.top - 1) / scale - dragHeight / 2;

            const filterArr = lineSite.filter((item: any, index: number) => {
                return e.clientY > item[0] && e.clientY < item[1]
            })
            const nearLineRow = lineSite.indexOf(filterArr[0])
            if (y <= 0) {
                this.setState({
                    y: '0',
                    nearLineRow: 0,
                    startDragLine: true
                })
            } else if (y >= this.tableControllerDom?.current.offsetHeight - dragHeight) {
                this.setState({
                    y: (this.tableControllerDom?.current.offsetHeight - dragHeight).toString(),
                    nearLineRow: this.rowHeight.length,
                    startDragLine: true
                })
            } else {
                this.setState({
                    y: y.toString(),
                    nearLineRow: this.mouseD ? nearLineRow + 1 : nearLineRow,
                    startDragLine: true
                })
            }
        }
    }

    // 拖拽行MouseUp
    onRowDragMouseUp = (e: MouseEvent) => {
        e.stopPropagation();
        const { nearLineRow, selectSize } = this.state;
        const ele: any = document.getElementsByClassName('assetEditBox')[0];
        if (ele) {
            ele.style.visibility = 'visible'
        }
        const outermostCanvasEle:any = document.getElementsByClassName('paintCanvaStyle')[0];
        outermostCanvasEle.style.cursor = 'auto';
        (this.tableControllerDom?.current as HTMLElement).style.cursor = '';
        const nearLine = this.mouseD ? nearLineRow - 1 : nearLineRow
        swapRow( nearLine, selectSize.endRow)
        const colWidthLength = this.colWidth.length;

        this.setState({
            isDragColAndRow: false,
            startDragLine: false,
            y: undefined,
            selectSize: {
                startRow: nearLine,
                endRow: nearLine,
                startCol: 0,
                endCol: colWidthLength - 1,
            }
        })
        this.DragflagColAndRow = true;
        this.ClientDragPosition = 0;
        this.mouseD = false;
        this.mouseT = false;
        this.eventManager.remove('rowDragMove');
        this.eventManager.remove('rowDragUp');
    }

    /** 行列选择 - 弹起 */
    onRowAndColSelectorMouseUp = (e: MouseEvent) => {
        e.stopPropagation();
        this.calcSelectedRowAndCol(e);
        this.selectRowAndCol = {
            type: undefined,
            row: undefined,
            col: undefined,
        };
        this.eventManager.remove('rowColMove');
        this.eventManager.remove('rowColUp');
    };

    /** 行列选择 - 移动 */
    onRowAndColSelectorMouseMove = (e: MouseEvent) => {
        e.stopPropagation();
        this.calcSelectedRowAndCol(e);
    };

    /** 修改悬停显示的加号 */
    changeAddIconIndex=(typeIndex: string)=>{
        this.setState({ addIconIndex: typeIndex});
    };

    /** 修改拖拽方块背景颜色 */
    changeHoverBgcIndex=(bgcIndex: string)=>{
        this.setState({ hoverBgcIndex: bgcIndex});
    };

    calcSelectedRowAndCol(e: MouseEvent) {
        if (this.tableControllerDom?.current?.contains(e.target as HTMLElement)) {
            const { type, row, col, model } = (e.target as HTMLElement).dataset;
            if (model === 'selectRowAndCol' && type === this.selectRowAndCol.type) {
                let startRow = 0;
                let endRow = 0;
                let startCol = 0;
                let endCol = 0;
                if (this.selectRowAndCol.type === 'row') {
                    startRow = Math.min(Number.parseInt(row), this.selectRowAndCol.row);
                    endRow = Math.max(Number.parseInt(row), this.selectRowAndCol.row);
                    const selectedTd: string[] = [];
                    const colLength = this.colWidth.length;
                    for (let i = startRow; i <= endRow; i++) {
                        for (let j = 0; j < colLength; j++) {
                            selectedTd.push(i + '_' + j);
                        }
                    }
                    this.setState({
                        choosedTdKeys: selectedTd,
                        selectSize: {
                            startRow,
                            endRow,
                            startCol: 0,
                            endCol: colLength - 1,
                        },
                    });
                    AssetLogic.updateTableSelectKey({
                        tdKeys: selectedTd,
                        className: this.props.asset.meta.className,
                    });
                }
                if (this.selectRowAndCol.type === 'col') {
                    startCol = Math.min(Number.parseInt(col), this.selectRowAndCol.col);
                    endCol = Math.max(Number.parseInt(col), this.selectRowAndCol.col);
                    const selectedTd: string[] = [];
                    const rowlLength = this.rowHeight.length;
                    for (let i = startCol; i <= endCol; i++) {
                        for (let j = 0; j < rowlLength; j++) {
                            selectedTd.push(j + '_' + i);
                        }
                    }
                    this.setState({
                        choosedTdKeys: selectedTd,
                        selectSize: {
                            startRow: 0,
                            endRow: rowlLength - 1,
                            startCol,
                            endCol,
                        },
                    });
                    AssetLogic.updateTableSelectKey({
                        tdKeys: selectedTd,
                        className: this.props.asset.meta.className,
                    });
                }
            }
        }
    }

    onTableKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
        if (this.props.isActive && this.state.choosedTdKeys?.length > 0) {
            if (!((e.ctrlKey || e.metaKey) && ['c', 'C', 'v', 'V'].includes(e.key))) {
                e.stopPropagation();
                e.nativeEvent?.stopImmediatePropagation();
            }
            if (e.key === 'Backspace' || e.key === 'Delete') {
                AssetLogic.updateAssetTableText({
                    tbKeys: this.state.choosedTdKeys,
                    updateType: 'content',
                    value: [''],
                    fun_name: 'DELETE_TABLE_CELLS_TEXT',
                });
            }
            if (
                !e.ctrlKey &&
                !e.metaKey &&
                !e.altKey &&
                /^[a-zA-Z0-9|`|~|!|@|#|$|%|^|&|*|(|)|_|+|-|=|<|>|?|,|.|/|\\]{1}$/.test(e.key)
            ) {
                const cell = this.state.choosedTdKeys[0];
                const row = cell.split('_')[0];
                const col = cell.split('_')[1];
                if (this.props.asset?.meta?.className) {
                    AssetLogic.updateTableText({
                        row: Number.parseInt(row),
                        col: Number.parseInt(col),
                        value: [''],
                        assetClassName: this.props.asset.meta.className,
                        assetIndex: this.props.index,
                    });
                }
                this.setState(
                    {
                        editingCell: cell,
                    },
                    () => {
                        const dom = this.tableDom?.current?.querySelector<HTMLDivElement>(
                            `.table-td-content-${row}-${col}`,
                        );
                        dom?.focus();
                        // const sel = window.getSelection(),
                        //     rang = document.createRange();
                        // rang.setStart(dom, 0);
                        // rang.setEnd(dom, 1);
                        // rang.collapse(false); //起始位置和终止位置是否相同的布尔值
                        // sel.removeAllRanges(); //移除选中区域的range对象
                        // sel.addRange(rang); //给选中区域添加range对象
                    },
                );
            }
        }
    };

    onTableKeyUp = (e: React.KeyboardEvent<HTMLElement>) => {
        if (this.props.isActive && this.state.choosedTdKeys?.length > 0) {
            e.stopPropagation();
            e.nativeEvent?.stopImmediatePropagation();
        }
    };

    componentDidUpdate(prevProps: Readonly<IAssetTable2Props>, prevState: Readonly<IAssetTable2State>): void {
        // 未激活时清空选中状态
        if (!this.props.isActive && this.state.choosedTdKeys?.length > 0 && this.props.asset?.meta?.className) {
            this.setState({
                choosedTdKeys: [],
                selectSize: undefined,
            });
            if (this.props.asset?.choosedTdKeys?.length > 0) {
                AssetLogic.updateTableSelectKey({
                    tdKeys: [],
                    className: this.props.asset.meta.className,
                });
            }
            return; // 早期返回，避免后续检查
        }

        // 处理外部选中状态变化
        if (this.props.isActive && prevProps.asset.choosedTdKeys !== this.props.asset.choosedTdKeys && this.props.asset.choosedTdKeys?.length === 0) {
            this.setState({
                choosedTdKeys: [],
                selectSize: undefined,
            });
            return; // 早期返回
        }

        // 激活状态变化时更新DOM位置信息
        if (this.props.isActive && this.props.isActive !== prevProps.isActive) {
            const tableControllerDomSite = (this.tableControllerDom?.current as HTMLElement)?.getBoundingClientRect();
            this.setState({
                tableControllerDomSite: tableControllerDomSite
            });
        }
    }

    componentWillUnmount(): void {
        if (this.props.asset?.meta?.className && this.lastInputInfo.row >= 0 && this.lastInputInfo.col >= 0) {
            AssetLogic.updateTableText(this.lastInputInfo);
        }
        AssetLogic.cancelTableSelectKey({tdKeys : [],className: this.props.asset?.meta?.className})
        // 使用事件管理器清理所有事件监听
        this.eventManager.removeAll();
        //
        window.removeEventListener('mousemove', this.onCellControllerMouseMove);
        window.removeEventListener('mouseup', this.onCellControllerMouseUp);
        //
        window.removeEventListener('mousemove', this.onRowAndColSelectorMouseMove);
        window.removeEventListener('mouseup', this.onRowAndColSelectorMouseUp);
        //
        window.removeEventListener('mousemove', this.onRowDragMove);
        window.removeEventListener('mouseup', this.onRowDragMouseUp);
        //
        window.removeEventListener('mousemove', this.onColDragMove);
        window.removeEventListener('mouseup', this.onColDragMouseUp);
    }

    /** 根据单元格输入框的实际高度计算最小高度 */
    getMinHeightFromSelection = (dragLineInfo?: {
        type: 'row' | 'col';
        lineIndex: number;
        direction: 'top' | 'bottom' | 'left' | 'right';
    }): number => {
        const { asset } = this.props;
        let minHeight = AssetTableLogic.minTableCellHeight || 16; // 默认最小值16px
        const paddingTotal = 10; // 上下padding各5px
        
        try {
            if (dragLineInfo) {
                if (dragLineInfo.type === 'row') {
                    // 行变更：检查当前行的所有单元格，计算最大的文本框高度
                    const targetRow = dragLineInfo.lineIndex === -1 ? 0 : dragLineInfo.lineIndex;
                    
                    console.log(`行拖拽，检查第${targetRow}行的文本框高度 (lineIndex: ${dragLineInfo.lineIndex})`);
                    
                    let maxTextHeight = 0;
                    
                    // 遍历目标行的所有列，获取实际DOM元素的高度
                    if (asset.attribute.text[targetRow]) {
                        Object.keys(asset.attribute.text[targetRow]).forEach(col => {
                            // 尝试获取对应的DOM元素
                            const contentElement = this.tableDom?.current?.querySelector<HTMLElement>(
                                `.table-td-content-${targetRow}-${col}`
                            );
                            
                            if (contentElement) {
                                // 使用scrollHeight获取内容的实际高度（包括溢出的部分）
                                const actualHeight = contentElement.scrollHeight;
                                maxTextHeight = Math.max(maxTextHeight, actualHeight);
                                console.log(`单元格[${targetRow}][${col}] DOM实际高度: ${actualHeight}px`);
                            } else {
                                // 如果没有DOM元素，使用文本属性进行估算
                                const textData = asset.attribute.text[targetRow][col];
                                const fontSize = textData.fontSize || 16;
                                const content = textData.content || [];
                                
                                // 计算文本行数
                                let lineCount = Math.max(1, content.length);
                                content.forEach((line: string) => {
                                    // 计算每行中的换行符
                                    const additionalLines = (line.match(/\n/g) || []).length;
                                    lineCount += additionalLines;
                                });
                                
                                // 估算高度：对于行间距，如果是无单位数字则乘以fontSize，否则直接使用
                                const lineHeight = textData.lineHeight || 1.2;
                                let lineHeightPx: number;
                                
                                if (typeof lineHeight === 'number' && lineHeight < 10) {
                                    // 无单位数字，乘以fontSize
                                    lineHeightPx = fontSize * lineHeight;
                                } else {
                                    // 已经是px值或其他单位
                                    lineHeightPx = Number(String(lineHeight).replace(/[^\d.]/g, '')) || fontSize * 1.2;
                                }
                                
                                const estimatedHeight = lineHeightPx * lineCount;
                                maxTextHeight = Math.max(maxTextHeight, estimatedHeight);
                                
                                console.log(`单元格[${targetRow}][${col}] 估算: fontSize=${fontSize}, lineHeight=${lineHeight}, lineHeightPx=${lineHeightPx}, 行数=${lineCount}, 高度=${estimatedHeight}`);
                            }
                        });
                    }
                    
                    // 最小高度 = 文本高度 + padding
                    minHeight = Math.max(minHeight, maxTextHeight + paddingTotal);
                    
                } else if (dragLineInfo.type === 'col') {
                    // 列变更：对于宽度，保持基本的最小宽度即可
                    const minWidth = AssetTableLogic.minTableCellWidth || 30;
                    minHeight = minWidth;
                }
            }
        } catch (error) {
            console.warn('计算文本框高度时出错:', error);
        }
        
        console.log('基于文本框计算的最小尺寸:', minHeight);
        return minHeight;
    };

    /** 表格拖动 - 移动 */
    onTableMoveMouseMove = (e: MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();

        // 检测鼠标移动距离是否超过5px阈值
        const mouseMoveDistance = Math.sqrt(
            Math.pow(e.clientX - this.tableDragStartX, 2) +
            Math.pow(e.clientY - this.tableDragStartY, 2)
        );

        // 如果还没开始拖动且移动距离小于5px，则不开始拖动
        if (!this.tableDragStarted && mouseMoveDistance < 5) {
            return;
        }

        // 如果还没开始拖动但移动距离超过5px，则开始拖动
        if (!this.tableDragStarted) {
            console.log('Starting table drag with 5px threshold...');
            this.tableDragStarted = true;

            // 修改鼠标样式
            document.body.style.cursor = 'move';

            // 更新拖动状态
            AssetLogic.updateAssetIsDrag({
                is_drag: true,
                fun_name: 'START_TABLE_DRAG',
            });
        }

        console.log('Table drag move:', e.clientX, e.clientY);

        const { canvas } = this.props;
        const scale = this.props.assetProps?.canvasScale ?? canvas?.scale ?? 1;

        // 计算移动的距离
        const deltaX = (e.clientX - this.tableDragStartX) / scale;
        const deltaY = (e.clientY - this.tableDragStartY) / scale;

        // 计算新的位置
        const newPosX = this.tableDragStartPosX + deltaX;
        const newPosY = this.tableDragStartPosY + deltaY;

        console.log('New position:', newPosX, newPosY);

        // 更新表格位置
        AssetDragLogic.dragAsset({
            posX: newPosX,
            posY: newPosY,
            fun_name: 'DRAG_TABLE',
        });
    };
    
    /** 表格拖动 - 弹起 */
    onTableMoveMouseUp = (e: MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();

        console.log('Table drag end');

        // 恢复鼠标样式
        document.body.style.cursor = '';

        // 只有在实际开始拖动后才结束拖动状态
        if (this.tableDragStarted) {
            // 结束拖动
            AssetDragLogic.dragAssetEnd({
                fun_name: 'END_TABLE_DRAG',
                isChange: true,
            });

            // 更新拖动状态
            AssetLogic.updateAssetIsDrag({
                is_drag: false,
                fun_name: 'END_TABLE_DRAG_STATUS',
            });
        }

        // 移除事件监听
        this.eventManager.remove('tableDragMove');
        this.eventManager.remove('tableDragUp');

        // 重置拖动相关属性
        this.tableDragStarted = false;
        this.tableDragStartX = 0;
        this.tableDragStartY = 0;
        this.tableDragStartPosX = 0;
        this.tableDragStartPosY = 0;
    };

    render() {
        const isPreview = this.props.assetProps && this.props.assetProps.showOnly;
        const {
            asset: {
                attribute: { cell, cellSize, text, width, height },
                meta: { className },
                is_drag,
            },
            canvas,
            isActive,
            hideDragTable,
        } = this.props;
        if (!cellSize?.row || !cellSize?.col) {
            return <></>;
        }
        const scale = this.props.assetProps?.canvasScale ?? canvas?.scale ?? 1;
        this.calcTotalRowAndCol(cellSize, width, height);
        const { x, y, dragWidth, dragHeight, isDragColAndRow, nearLineRow, nearLineCol, startDragLine, addIconIndex, hoverBgcIndex } = this.state;

        return (
            <div
                className="table-asset-panel-container asset-table2"
                style={{
                    width,
                    height,
                    transform: `scale(${scale})`,
                    cursor: this.state.choosedTdKeys?.length > 0 ? 'default' : 'move'
                }}
                tabIndex={0}
                onKeyDown={this.onTableKeyDown}
                onKeyUp={this.onTableKeyUp}
                onMouseDown={this.onTableMouseDown}
            >
                <div className="table-warper">
                    <table className="table-main" ref={this.tableDom}>
                        <colgroup>
                            {this.colWidth.map((c, index) => {
                                return <col key={index} span={1} style={{ width: c }} />;
                            })}
                        </colgroup>
                        <tbody>
                            {cell.map((r, row) => {
                                return (
                                    <Tr key={row} row={row} height={this.rowHeight[row]}>
                                        {r.map((c, col) => {
                                            return (
                                                <Td
                                                    key={row + '-' + col}
                                                    width={this.colWidth[col]}
                                                    height={this.rowHeight[row]}
                                                    row={row}
                                                    col={col}
                                                    cell={c}
                                                    text={text[row][col]}
                                                    contentEditable={
                                                        !isPreview &&
                                                        isActive &&
                                                        this.state.editingCell === `${row}_${col}`
                                                    }
                                                    onFocus={this.onTdContentFocus}
                                                    scale={scale}
                                                    onInput={this.onInput}
                                                    onBlur={this.onBlur}
                                                    onTdContentKeyDown={this.onTdContentKeyDown}
                                                    onTdContentKeyUp={this.onTdContentKeyUp}
                                                ></Td>
                                            );
                                        })}
                                    </Tr>
                                );
                            })}
                        </tbody>
                    </table>
                </div>
                {!isPreview && (
                    <div className="table-controller" ref={this.tableControllerDom}>
                        <CellController
                            cells={cell}
                            rowHeight={this.rowHeight}
                            colWidth={this.colWidth}
                            editingCell={this.state.editingCell}
                            onCellControllerMouseDown={this.onCellControllerMouseDown}
                            onCellControllerDoubleClick={this.onCellControllerDoubleClick}
                        />
                        {isActive && (
                            <div style={{ display: !isActive ? 'none' : 'block' }}>
                                <SelectedTds
                                    cells={cell}
                                    rowHeight={this.rowHeight}
                                    colWidth={this.colWidth}
                                    scale={scale}
                                    selectSize={this.state.selectSize}
                                    isDragColAndRow={isDragColAndRow}
                                />
                                {
                                    !isDragColAndRow &&
                                    <RowAndColLines
                                        rowHeight={this.rowHeight}
                                        colWidth={this.colWidth}
                                        scale={scale}
                                        cn={className}
                                        isDragging={is_drag}
                                        activeControllerLine={this.state.activeControllerLine}
                                        onMouseDown={this.onTableControllerLineMouseDown}
                                        addIconIndex={addIconIndex}
                                        cells={cell}
                                    />
                                }
                                <div style={{ display: this.state.editingCell ? 'none' : 'block' }}>
                                            {
                                                startDragLine &&
                                                <ShowRestDragLine 
                                                    rowHeight={this.rowHeight}
                                                    colWidth={this.colWidth}
                                                    scale={scale}
                                                    nearLineCol={nearLineCol}
                                                    nearLineRow={nearLineRow}
                                                    type={this.selectRowAndCol.type}
                                                />
                                            }
                                        </div>
                                        {
                                            isDragColAndRow &&
                                            <SelectColAndRowDrag 
                                                cells={cell}
                                                rowHeight={this.rowHeight}
                                                colWidth={this.colWidth}
                                                scale={scale}
                                                selectSize={this.state.selectSize}
                                                dragWidth={dragWidth}
                                                dragHeight={dragHeight}
                                                x={x}
                                                y={y}
                                                type={this.selectRowAndCol.type}
                                            />
                                        }
                                        {
                                            !isDragColAndRow &&
                                            <RowAndColSelect
                                                rowHeight={this.rowHeight}
                                                colWidth={this.colWidth}
                                                scale={scale}
                                                selectSize={this.state.selectSize}
                                                cells={cell}
                                                activeControllerLine={this.state.activeControllerLine}
                                                isDragging={is_drag}
                                                onSelectorMouseDown={this.onRowAndColSelectorMouseDown}
                                                changeAddIconIndex={this.changeAddIconIndex}
                                                hoverBgcIndex={hoverBgcIndex}
                                                changeHoverBgcIndex={this.changeHoverBgcIndex}
                                        />
                                        }
                                        {
                                            !hideDragTable && !isDragColAndRow &&
                                            <MoveController 
                                                scale={scale} 
                                                activeControllerLine={this.state.activeControllerLine}
                                                isDragging={is_drag}
                                            />
                                        }

                                        {/* {
                                            !isDragColAndRow && 
                                            <RightAddCol
                                                activeControllerLine={this.state.activeControllerLine}
                                                isDragging={is_drag}
                                                scale={scale}
                                                maxRow={cellSize.row.length - 1}
                                                maxCol={cellSize.col.length - 1}
                                            />
                                        } */}

                                        {/* {
                                            !isDragColAndRow && 
                                            <BottomAddRow
                                                activeControllerLine={this.state.activeControllerLine}
                                                isDragging={is_drag}
                                                scale={scale}
                                                maxRow={cellSize.row.length - 1}
                                                maxCol={cellSize.col.length - 1}
                                            />
                                        } */}
                            </div>
                        )}
                    </div>
                )}
            </div>
        );
    }
}

function Tr(props: { row: number; height: number; children: React.ReactNode }) {
    return <tr style={{ height: props.height }}>{props.children}</tr>;
}

function Td(props: {
    width: number;
    height: number;
    row: number;
    col: number;
    cell: ICell;
    text: IText;
    contentEditable: boolean;
    scale: number;
    onInput: (e: React.FormEvent<HTMLElement>) => void;
    onBlur: (e: React.FormEvent<HTMLElement>) => void;
    onFocus: (e: React.FocusEvent<HTMLElement>) => void;
    onTdContentKeyDown: (e: React.KeyboardEvent<HTMLElement>) => void;
    onTdContentKeyUp: (e: React.KeyboardEvent<HTMLElement>) => void;
}) {
    const { text, row, col, cell, contentEditable, onBlur, onInput, onFocus, onTdContentKeyDown, onTdContentKeyUp } = props;
    if (cell.merged) {
        return <></>;
    }
    
    return (
        <td
            // x 是行 y 是列
            colSpan={cell.merge ? Math.max(cell.merge.y - col + 1, 1) : 1}
            rowSpan={cell.merge ? Math.max(cell.merge.x - row + 1, 1) : 1}
            style={{
                background: `rgba(${cell.background.color.r},${cell.background.color.g},${cell.background.color.b},${cell.background.color.a})`,
                borderTop: `${Math.max(Math.round(cell.lineWidth[0] / props.scale), 0)}px ${cell.lineStyle[0]} rgba(${
                    cell.lineColor[0].r
                },${cell.lineColor[0].g},${cell.lineColor[0].b},${cell.lineColor[0].a})`,
                borderRight: `${Math.max(Math.round(cell.lineWidth[1] / props.scale), 0)}px ${cell.lineStyle[1]} rgba(${
                    cell.lineColor[1].r
                },${cell.lineColor[1].g},${cell.lineColor[1].b},${cell.lineColor[1].a})`,
                borderBottom: `${Math.max(Math.round(cell.lineWidth[2] / props.scale), 0)}px ${cell.lineStyle[2]} rgba(${
                    cell.lineColor[2].r
                },${cell.lineColor[2].g},${cell.lineColor[2].b},${cell.lineColor[2].a})`,
                borderLeft: `${Math.max(Math.round(cell.lineWidth[3] / props.scale),0)}px ${cell.lineStyle[3]} rgba(${
                    cell.lineColor[3].r
                },${cell.lineColor[3].g},${cell.lineColor[3].b},${cell.lineColor[3].a})`,
            }}
        >
            <div className="table-td-container">
                <div
                    className={`table-td-content table-td-content-${row}-${col}`}
                    style={{
                        color: `rgba(${text.color.r},${text.color.g},${text.color.b},${text.color.a})`,
                        fontFamily: fontNameValueList[text.fontFamily],
                        fontSize: text.fontSize,
                        fontWeight: text.fontWeight,
                        lineHeight: text.lineHeight,
                        textAlign: text.textAlign,
                        fontStyle: text.fontStyle,
                        textDecoration: text.textDecoration,
                        letterSpacing: text.letterSpacing,
                    }}
                    data-row={row}
                    data-col={col}
                    data-cell={row + '_' + col}
                    spellCheck={false}
                    suppressContentEditableWarning={true}
                    contentEditable={contentEditable ? ('plaintext-only' as any) : false}
                    onBlur={onBlur}
                    onInput={onInput}
                    onFocus={onFocus}
                    onKeyDown={onTdContentKeyDown}
                    onKeyUp={onTdContentKeyUp}
                    onPaste={onTdContentPaste}
                >
                    {text.content.join('\n')}
                </div>
            </div>
        </td>
    );
}
