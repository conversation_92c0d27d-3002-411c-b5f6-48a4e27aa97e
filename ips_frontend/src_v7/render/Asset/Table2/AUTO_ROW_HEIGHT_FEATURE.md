# 表格自动行高调整功能

## 功能描述

当用户横向拖动表格列边框修改列宽时，如果文字因为列宽变化而发生换行，系统会自动调整该行的高度以适应文字的显示需求。

## 实现原理

1. **触发时机**: 在列宽调整完成时（`onTableControllerLineMouseUp`函数中）
2. **检测机制**: 遍历每一行的所有单元格，计算文本内容所需的最小高度
3. **调整策略**: 如果计算出的文本高度大于当前行高，则自动增加行高

## 核心代码

### 1. AssetTableLogic.autoAdjustRowHeights()

新增的静态方法，负责：
- 计算每行文本的实际高度需求
- 考虑字体大小、行间距、换行符等因素
- 更新表格的行高配置和总高度

### 2. utils.ts 中的触发逻辑

在 `onTableControllerLineMouseUp` 函数中：
- 检测是否为列宽调整操作
- 延迟100ms执行自动调整（确保列宽更新完成）
- 调用 `AssetTableLogic.autoAdjustRowHeights()`

## 计算逻辑

### 文本高度计算

```typescript
// 计算文本行数
let lineCount = Math.max(1, content.length);
content.forEach((line: string) => {
    const additionalLines = (line.match(/\n/g) || []).length;
    lineCount += additionalLines;
});

// 计算行间距像素值
let lineHeightPx: number;
if (typeof lineHeight === 'number' && lineHeight < 10) {
    lineHeightPx = fontSize * lineHeight; // 无单位数字
} else {
    lineHeightPx = Number(String(lineHeight).replace(/[^\d.]/g, '')) || fontSize * 1.2;
}

// 最终高度 = 文本高度 + 内边距
const estimatedTextHeight = lineHeightPx * lineCount;
const paddingTotal = 10; // 上下padding各5px
const requiredCellHeight = estimatedTextHeight + paddingTotal;
```

### 行高调整

- 只有当计算出的所需高度大于当前行高时才进行调整
- 保持行高比例关系，同时更新表格总高度
- 确保最小行高不低于 `AssetTableLogic.minTableCellHeight`

## 使用场景

1. **文字换行**: 列宽变窄导致文字换行时，自动增加行高
2. **多行文本**: 包含换行符的文本内容能够完整显示
3. **不同字体大小**: 根据实际字体大小计算合适的行高

## 注意事项

1. **性能考虑**: 使用100ms延迟避免频繁计算
2. **最小高度**: 始终保持不低于最小行高限制
3. **只增不减**: 当前实现只会增加行高，不会自动减少
4. **错误处理**: 包含try-catch确保功能稳定性

## 测试方法

### 手动测试
1. 创建一个包含较长文本的表格
2. 拖动列边框使列宽变窄
3. 观察文字换行后行高是否自动调整
4. 验证所有文字都能完整显示

### 代码测试
```typescript
// 在浏览器控制台中运行
import { testAutoRowHeightAdjustment, testTextHeightCalculation } from './test_auto_row_height';

// 测试文本高度计算
testTextHeightCalculation();

// 测试自动调整功能（需要在有表格的页面中运行）
testAutoRowHeightAdjustment();
```

### 调试信息
- 打开浏览器开发者工具
- 在控制台中查看调整过程的日志输出
- 关键日志包括：
  - "列拖动结束，开始重新计算行高以适应文字换行"
  - "行 X 需要调整高度: Y -> Z"
  - "表格行高已自动调整以适应文字换行"

## 技术细节

### 容差处理
- 添加2px容差避免微小差异导致的频繁调整
- 只有当所需高度明显大于当前高度时才进行调整

### 性能优化
- 使用100ms延迟确保列宽更新完成
- 只在列宽调整时触发，不影响其他操作
- 批量更新所有需要调整的行

### 错误处理
- 完整的try-catch包装
- 数据完整性检查
- 优雅的降级处理

## 未来优化

1. **智能减少行高**: 当列宽增加时自动减少不必要的行高
2. **更精确的文本测量**:
   - 考虑字体族、字重、字间距
   - 使用Canvas或DOM测量实际文本尺寸
3. **批量调整优化**: 当多列同时调整时的性能优化
4. **用户配置**:
   - 自动调整开关
   - 最小/最大行高限制
   - 调整敏感度设置
5. **动画效果**: 平滑的行高变化动画
6. **撤销/重做**: 支持行高调整的撤销操作
