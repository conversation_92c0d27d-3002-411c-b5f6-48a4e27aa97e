import { AssetLogic } from '@v7_logic/AssetLogic';
import { AssetTableLogic } from '@v7_logic/AssetTableLogic';
import { IAsset } from '@v7_logic/Interface';
import React from 'react';
import { IAssetTable2State } from './Table2';
import {storeAdapter} from '@v7_logic_core/StoreAdapter';
import { assetManager } from '@component/AssetManager';

export const calcSelectedTds = (cells: IAsset['attribute']['cell'], start: string, end: string) => {
    if (!start || !end) {
        return { selectedTd: [] as string[] };
    }
    const startArr = start.split('_');
    const endArr = end.split('_');
    let startX = Math.min(parseInt(startArr[0]), parseInt(endArr[0]));
    let endX = Math.max(parseInt(startArr[0]), parseInt(endArr[0]));
    let startY = Math.min(parseInt(startArr[1]), parseInt(endArr[1]));
    let endY = Math.max(parseInt(startArr[1]), parseInt(endArr[1]));
    if (start == end) {
        const merge = cells[startX][startY].merge; // 处理选择单个单元格
        return {
            selectedTd: [start],
            size: { startRow: startX, startCol: startY, endRow: merge?.x ?? endX, endCol: merge?.y ?? endY },
        };
    }
    let minX = startX;
    let minY = startY;
    let maxX = endX;
    let maxY = endY;
    ({ startX, startY, endX, endY } = checkTdSelect(minX, minY, maxX, maxY, startX, startY, endX, endY, cells));

    while (minX !== startX || minY !== startY || maxX !== endX || maxY !== endY) {
        minX = startX;
        minY = startY;
        maxX = endX;
        maxY = endY;
        ({ startX, startY, endX, endY } = checkTdSelect(minX, minY, maxX, maxY, startX, startY, endX, endY, cells));
    }

    const resKeys = [];
    let _x = startX;
    while (_x <= endX) {
        let _y = startY;
        while (_y <= endY) {
            resKeys.push(_x + '_' + _y);
            _y++;
        }
        _x++;
    }
    return { selectedTd: resKeys, size: { startRow: startX, startCol: startY, endRow: endX, endCol: endY } };
};

export const checkTdSelect = (
    minX: number,
    minY: number,
    maxX: number,
    maxY: number,
    startX: number,
    startY: number,
    endX: number,
    endY: number,
    cells: IAsset['attribute']['cell'],
) => {
    for (let i = startX; i <= endX; i++) {
        for (let j = startY; j <= endY; j++) {
            const td = cells[i][j];
            if (td.merge) {
                maxX = Math.max(maxX, td.merge.x);
                maxY = Math.max(maxY, td.merge.y);
            }
            if (td.merged) {
                minX = Math.min(minX, td.merged.x);
                minY = Math.min(minY, td.merged.y);
            }
        }
    }
    return {
        startX: minX,
        startY: minY,
        endX: maxX,
        endY: maxY,
    };
};

type TLineController = {
    type: 'row' | 'col';
    rowline: number;
    colline: number;
    scale: number;
    tempLine: HTMLElement;
    startX: number;
    startY: number;
    startTop: number;
    startleft: number;
    cn: string;
    setState: (state: Pick<IAssetTable2State, 'activeControllerLine'>) => void;
    getMinHeight?: (dragLineInfo: { type: 'row' | 'col'; lineIndex: number; direction: 'top' | 'bottom' | 'left' | 'right'; }) => number;
};

let lineController: TLineController = {
    type: undefined,
    rowline: undefined,
    colline: undefined,
    scale: undefined,
    tempLine: undefined,
    startX: undefined,
    startY: undefined,
    startTop: undefined,
    startleft: undefined,
    cn: undefined,
    setState: undefined,
};

export const onTableControllerLineMouseDown = (
    e: React.MouseEvent<HTMLElement>,
    setState: (state: Pick<IAssetTable2State, 'activeControllerLine'>) => void,
    getMinHeight?: (dragLineInfo: { type: 'row' | 'col'; lineIndex: number; direction: 'top' | 'bottom' | 'left' | 'right'; }) => number,
) => {
    e.stopPropagation();
    e.nativeEvent?.stopPropagation();
    const { type, rowline, colline, scale, cn } = e.currentTarget.dataset;
    lineController.type = type as 'row' | 'col';
    lineController.rowline = rowline ? Number.parseInt(rowline) : undefined;
    lineController.colline = colline ? Number.parseInt(colline) : undefined;
    lineController.scale = scale ? Number.parseFloat(scale) : undefined;
    lineController.startX = e.clientX;
    lineController.startY = e.clientY;
    lineController.startTop = type === 'row' ? Number.parseFloat(e.currentTarget.style.top) : undefined;
    lineController.startleft = type === 'col' ? Number.parseFloat(e.currentTarget.style.left) : undefined;
    lineController.cn = cn;
    lineController.setState = setState;
    lineController.getMinHeight = getMinHeight;
    lineController.setState({
        activeControllerLine: type + '_' + (rowline ?? colline),
    });
    lineController.tempLine = e.currentTarget;
    window.addEventListener('mousemove', onTableControllerLineMouseMove);
    window.addEventListener('mouseup', onTableControllerLineMouseUp);
};

const onTableControllerLineMouseMove = (e: MouseEvent) => {
    e.stopPropagation();
    const movedTop = e.clientY - lineController.startY;
    const movedLeft = e.clientX - lineController.startX;
    const scale = lineController.scale || 1;
    
    if (lineController.type === 'row') {
        // 获取当前行的高度（实际像素高度）
        const currentRowHeight = Number(lineController.tempLine.dataset.h);
        console.log('currentRowHeight', currentRowHeight);
        console.log('scale', scale);
        
        // 计算拖拽后的新高度（考虑缩放因子）
        const heightChange = (lineController.rowline === -1 ? -movedTop : movedTop) / scale;
        const newRowHeight = currentRowHeight + heightChange;
        console.log('heightChange', heightChange);
        console.log('newRowHeight', newRowHeight);
        
        // 获取动态最小高度（基于相邻行/列的最大字号）
        const dragLineInfo = {
            type: lineController.type,
            lineIndex: lineController.rowline, // 直接使用行索引
            direction: lineController.rowline === -1 ? 'top' : 'bottom'
        } as {
            type: 'row' | 'col';
            lineIndex: number;
            direction: 'top' | 'bottom' | 'left' | 'right';
        };
        
        const minHeight = lineController.getMinHeight ? lineController.getMinHeight(dragLineInfo) : AssetTableLogic.minTableCellHeight;
        console.log('动态最小高度:', minHeight);
        console.log('拖拽线信息:', dragLineInfo);
        
        // 检查新高度是否小于动态最小高度
        if (newRowHeight < minHeight) {
            console.log(`新高度 ${newRowHeight} 小于最小高度 ${minHeight}，保持在最小高度`);
            // 达到最小限制时，不更新AssetLogic，但仍然更新起始位置保持拖动连续性
            lineController.startY = e.clientY;
            return;
        }
        console.log('movedTop', movedTop);
        
        AssetLogic.updateTableTdScale({
            direction: lineController.rowline === -1 ? 'top' : 'bottom',
            pointIndex: lineController.rowline === -1 ? 0 : lineController.rowline,
            value: movedTop,
            tableClass: lineController.cn,
            dynamicMinHeight: minHeight,
        });
        lineController.startY = e.clientY;
    } else if (lineController.type === 'col') {
        // 获取当前列的宽度（实际像素宽度）
        const currentColWidth = Number(lineController.tempLine.dataset.w);
        
        // 计算拖拽后的新宽度（考虑缩放因子）
        const widthChange = (lineController.colline === -1 ? -movedLeft : movedLeft) / scale;
        const newColWidth = currentColWidth + widthChange;
        
        // 构造列拖拽线信息
        const dragLineInfo = {
            type: lineController.type,
            lineIndex: lineController.colline, // 直接使用列索引
            direction: lineController.colline === -1 ? 'left' : 'right'
        } as {
            type: 'row' | 'col';
            lineIndex: number;
            direction: 'top' | 'bottom' | 'left' | 'right';
        };
        
        // 获取动态最小宽度（基于相邻列的最大字号）
        const minWidth = lineController.getMinHeight ? 
            Math.max(lineController.getMinHeight(dragLineInfo), AssetTableLogic.minTableCellWidth) : 
            AssetTableLogic.minTableCellWidth;
        
        console.log('列拖拽 - 动态最小宽度:', minWidth);
        console.log('列拖拽 - 拖拽线信息:', dragLineInfo);
        
        // 检查新宽度是否小于最小宽度
        if (newColWidth < minWidth) {
            console.log(`新宽度 ${newColWidth} 小于最小宽度 ${minWidth}，保持在最小宽度`);
            // 达到最小限制时，不更新AssetLogic，但仍然更新起始位置保持拖动连续性
            lineController.startX = e.clientX;
            return;
        }
        
        AssetLogic.updateTableTdScale({
            direction: lineController.colline === -1 ? 'left' : 'right',
            pointIndex: lineController.colline === -1 ? 0 : lineController.colline,
            value: movedLeft,
            tableClass: lineController.cn,
            dynamicMinHeight: minWidth, // 对于列，传递动态最小宽度
        });
        lineController.startX = e.clientX;
    }
};

const onTableControllerLineMouseUp = (e: MouseEvent) => {
    e.stopPropagation();

    console.log('onTableControllerLineMouseUp 被调用', {
        type: lineController.type,
        cn: lineController.cn,
        rowline: lineController.rowline,
        colline: lineController.colline
    });

    // 如果是列拖动，触发行高重新计算
    if (lineController.type === 'col' && lineController.cn) {
        console.log('列拖动结束，开始重新计算行高以适应文字换行');

        // 延迟执行，确保列宽更新完成后再调整行高
        setTimeout(() => {
            try {
                // 从className中提取assetIndex
                const { toolPanel } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
                    store_name: storeAdapter.store_names.paintOnCanvas,
                });

                console.log('toolPanel.asset_index:', toolPanel.asset_index);

                if (typeof toolPanel.asset_index === 'number' && toolPanel.asset_index >= 0) {
                    const className = lineController.cn;
                    console.log('调用 autoAdjustRowHeights，参数:', {
                        assetClassName: className,
                        assetIndex: toolPanel.asset_index,
                        fun_name: 'AUTO_ADJUST_ROW_HEIGHTS_AFTER_COL_RESIZE',
                    });
                    console.log('lineController.cn 的值:', lineController.cn);

                    AssetTableLogic.autoAdjustRowHeights({
                        assetClassName: className,
                        assetIndex: toolPanel.asset_index,
                        fun_name: 'AUTO_ADJUST_ROW_HEIGHTS_AFTER_COL_RESIZE',
                    });
                } else {
                    console.log('asset_index 无效:', toolPanel.asset_index);
                }
            } catch (error) {
                console.error('自动调整行高时出错:', error);
            }
        }, 100); // 100ms延迟，确保列宽更新完成
    } else {
        console.log('不是列拖动或缺少className:', {
            type: lineController.type,
            cn: lineController.cn
        });
    }

    window.removeEventListener('mousemove', onTableControllerLineMouseMove);
    window.removeEventListener('mouseup', onTableControllerLineMouseUp);
    // lineController.tempLine.parentNode.removeChild(lineController.tempLine);
    lineController.setState({
        activeControllerLine: undefined,
    });
    lineController = {
        type: undefined,
        rowline: undefined,
        colline: undefined,
        scale: undefined,
        tempLine: undefined,
        startX: undefined,
        startY: undefined,
        startTop: undefined,
        startleft: undefined,
        cn: undefined,
        setState: undefined,
    };
};

export const tableAddMaxRowAndMaxCol = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    assetManager.setPv_new(7061)
    e.nativeEvent?.stopPropagation();
    const { type, maxRow, maxCol } = e.currentTarget.dataset;
    const row = Number.parseInt(maxRow);
    const col = Number.parseInt(maxCol);
    if (type === 'col') {
        AssetLogic.tableRightMenuInsertRight({ minRow: row, maxRow: row, minCol: col, maxCol: col });
    } else if (type === 'row') {
        AssetLogic.tableRightMenuInsertBottom({ minRow: row, maxRow: row, minCol: col, maxCol: col });
    }
};

/**
 * @description: 点击删除table行列
 * @param {string} type
 */
export const deleteRowAndColFn = (type: string, series: number) => {
    return () => {
        const { toolPanel  } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const cellSize = toolPanel.asset.attribute.cellSize
        const maxRow = cellSize.row.length - 1;
        const maxCol = cellSize.row.length - 1;
        let cellMaxRowKey = 0;
        let cellMaxColKey = 0;
        for(const row  in toolPanel.asset.attribute.cell){
            const colData = toolPanel.asset.attribute.cell[row];
            cellMaxRowKey = cellMaxRowKey > parseInt(row)  ? cellMaxRowKey : parseInt(row);
            for(const col in colData){
                cellMaxColKey = cellMaxColKey > parseInt(col) ? cellMaxColKey : parseInt(col);
            }
        }
        if (type === 'row') {
            assetManager.setPv_new(7065)
            if(maxRow == 0 && maxCol == 0 && cellMaxRowKey == maxRow){
                AssetLogic.deleteSelectAsset();
            }else{
                AssetLogic.tableRightMenuDeleteRow(
                    {
                        minRow: Number(series),maxRow: Number(series),minCol: maxCol,maxCol
                    }
                )
            }
        }
        if (type === 'col') {
            assetManager.setPv_new(7065)
            if(maxRow==0 && maxCol == 0 && cellMaxColKey == maxCol){
                AssetLogic.deleteSelectAsset();
            }else{
                AssetLogic.tableRightMenuDeleteCol(
                    {
                        minRow: maxRow,maxRow,minCol: Number(series),maxCol: Number(series)
                    }
                )
            }
        }
    }
}

/**
 * @description: 插入行列
 */

export const addCol = (e: React.MouseEvent<HTMLElement>) => {
    e.stopPropagation();
    assetManager.setPv_new(7060);
    const key = (e.target as HTMLElement).dataset.serial;
    if (key === '-1') {
        addColAndRowFn('col', 'left', key)
    } else {
        addColAndRowFn('col', 'right', key)
    }
}

export const addRow = (e: React.MouseEvent<HTMLElement>) => {
    const key = (e.target as HTMLElement).dataset.serial;
    e.stopPropagation();
    assetManager.setPv_new(7060);
    if (key === '-1') {
        addColAndRowFn('row', 'top', key)
    } else {
        addColAndRowFn('row', 'bottom', key)
    }
}

export const addColAndRowFn = (type: string, direction: string, series: string) => {
    const { toolPanel  } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
        store_name: storeAdapter.store_names.paintOnCanvas,
    });
    const cellSize = toolPanel.asset.attribute.cellSize
    const maxRow = cellSize.row.length - 1;
    const maxCol = cellSize.row.length - 1;
    if (type === 'col') {
        if (direction === 'right') {
            AssetLogic.tableRightMenuInsertRight(
                {
                    minRow: maxRow,maxRow,minCol: Number(series),maxCol: Number(series)
                }
            )
        }
        if (direction === 'left') {
            AssetLogic.tableRightMenuInsertLeft(
                {
                    minRow: maxRow,maxRow,minCol:0,maxCol: 0
                }
            )
        }
    }
    if (type === 'row') {
        if (direction === 'top') {
            AssetLogic.tableRightMenuInsertTop(
                {
                    minRow:0,maxRow:0,minCol: maxCol,maxCol
                }
            )
        }
        if (direction === 'bottom') {
            AssetLogic.tableRightMenuInsertBottom(
                {
                    minRow: Number(series),maxRow: Number(series),minCol: maxCol,maxCol
                }
            )
        }
    }
}

// 拖拽列弹起时交换位置
export const swapCol = (nearLine: number, startLine: number) => {
    AssetTableLogic.drageTableCol({
        start: startLine,
        end: nearLine
    })
}

// 拖拽列弹起时交换位置
export const swapRow = (nearLine: number, startLine: number) => {
    AssetTableLogic.drageTableRow({
        start: startLine,
        end: nearLine
    })
}


