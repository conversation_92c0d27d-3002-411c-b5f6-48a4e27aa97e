/**
 * 测试表格自动行高调整功能
 * 这个文件包含了测试用例和示例数据
 */

import { AssetTableLogic } from '@v7_logic/AssetTableLogic';

// 模拟测试数据
const mockTableData = {
    assetClassName: 'test-table-123',
    assetIndex: 0,
    fun_name: 'TEST_AUTO_ADJUST_ROW_HEIGHTS',
    pageIndex: 0,
};

// 模拟的表格文本数据（包含长文本和换行）
const mockTextData = [
    [
        {
            content: ['这是一段很长的文字，当列宽变窄时应该会换行'],
            fontSize: 16,
            lineHeight: 1.2,
        },
        {
            content: ['短文本'],
            fontSize: 16,
            lineHeight: 1.2,
        }
    ],
    [
        {
            content: ['多行文本\n第二行\n第三行'],
            fontSize: 18,
            lineHeight: 1.5,
        },
        {
            content: ['另一个单元格的内容'],
            fontSize: 14,
            lineHeight: 1.3,
        }
    ]
];

// 模拟的单元格数据
const mockCellData = [
    [
        { lineStyle: 'solid', lineColor: { r: 162, g: 162, b: 162, a: 1 } },
        { lineStyle: 'solid', lineColor: { r: 162, g: 162, b: 162, a: 1 } }
    ],
    [
        { lineStyle: 'solid', lineColor: { r: 162, g: 162, b: 162, a: 1 } },
        { lineStyle: 'solid', lineColor: { r: 162, g: 162, b: 162, a: 1 } }
    ]
];

// 模拟的尺寸数据
const mockCellSize = {
    row: [30, 30], // 初始行高
    col: [100, 100] // 列宽
};

/**
 * 测试自动行高调整功能
 * 注意：这个测试需要在实际的应用环境中运行，因为它依赖于storeAdapter
 */
export function testAutoRowHeightAdjustment() {
    console.log('开始测试表格自动行高调整功能...');
    
    try {
        // 调用自动调整行高方法
        AssetTableLogic.autoAdjustRowHeights(mockTableData);
        
        console.log('测试完成，请检查控制台输出和表格变化');
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    }
}

/**
 * 计算文本高度的辅助函数（用于验证计算逻辑）
 */
export function calculateTextHeight(textData: any): number {
    const fontSize = textData.fontSize || 16;
    const lineHeight = textData.lineHeight || 1.2;
    const content = textData.content || [];
    
    // 计算文本行数
    let lineCount = Math.max(1, content.length);
    content.forEach((line: string) => {
        if (typeof line === 'string') {
            const additionalLines = (line.match(/\n/g) || []).length;
            lineCount += additionalLines;
        }
    });

    // 计算行间距像素值
    let lineHeightPx: number;
    if (typeof lineHeight === 'number' && lineHeight < 10) {
        lineHeightPx = fontSize * lineHeight;
    } else {
        lineHeightPx = Number(String(lineHeight).replace(/[^\d.]/g, '')) || fontSize * 1.2;
    }

    const estimatedTextHeight = lineHeightPx * lineCount;
    const paddingTotal = 10; // 上下padding各5px
    
    return estimatedTextHeight + paddingTotal;
}

/**
 * 测试文本高度计算
 */
export function testTextHeightCalculation() {
    console.log('测试文本高度计算...');
    
    const testCases = [
        {
            name: '单行短文本',
            data: { content: ['短文本'], fontSize: 16, lineHeight: 1.2 }
        },
        {
            name: '单行长文本',
            data: { content: ['这是一段很长的文字，当列宽变窄时应该会换行'], fontSize: 16, lineHeight: 1.2 }
        },
        {
            name: '多行文本',
            data: { content: ['第一行\n第二行\n第三行'], fontSize: 18, lineHeight: 1.5 }
        },
        {
            name: '多个内容数组',
            data: { content: ['第一段', '第二段', '第三段'], fontSize: 14, lineHeight: 1.3 }
        }
    ];
    
    testCases.forEach(testCase => {
        const height = calculateTextHeight(testCase.data);
        console.log(`${testCase.name}: 计算高度 = ${height.toFixed(1)}px`);
    });
}

// 导出测试函数，可以在浏览器控制台中调用
if (typeof window !== 'undefined') {
    (window as any).testAutoRowHeight = testAutoRowHeightAdjustment;
    (window as any).testTextHeight = testTextHeightCalculation;
    (window as any).calculateTextHeight = calculateTextHeight;
}
