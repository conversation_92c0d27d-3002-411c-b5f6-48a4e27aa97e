import { IAsset, ICellSize, ICell, IText, IColor } from '@v7_logic/Interface';
import { UpdateAsset } from '@v7_logic/AssetLogic';
// import { emitter } from "../../../src/userComponentV6.0/Emitter";
import { assetManager } from '@component/AssetManager';
import { storeAdapter } from '@v7_logic_core/StoreAdapter';
import { klona as cloneDeep } from 'klona';
import { AssetHelper } from '@v7_logic/AssetHelper';

type TMergeInfo = {
    key: string;
    startRow: number;
    startCol: number;
    endRow: number;
    endCol: number;
}[];

/**
 * 表格 asset 的逻辑
 */
export class AssetTableLogic {
    public static minTableCellWidth = 10;
    public static minTableCellHeight = 16;
    public static defaultCell = {
        lineStyle: 'solid',
        lineColor : { r: 162, g: 162, b: 162, a: 1 },
        lineWidth : 1,
        bgColor : { r: 255, g: 255, b: 255, a: 1 },
        fontColor : { r: 51, g: 51, b: 51, a: 1 },
        fontFaimly: 'fnsyhtRegular',
        fontSize: 48,
    }
    /**
     * 更新table
     */
    public static updateStyle(params: { asset: IAsset; assetIndex: number; fun_name: string }): void {
        const { asset, fun_name, assetIndex } = params;
        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: asset.attribute,
                    transform: asset.transform,
                    meta: asset.meta,
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /**
     * 更新table中字体及样式
     */
    public static updateText(params: { tbKeys: string[]; updateType: string; value: string | string[]; fun_name: string }): void {
        const { tbKeys = [], updateType, value, fun_name } = params;

        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });
        const text = cloneDeep(asset.attribute.text);

        tbKeys.map((v: string) => {
            const keysArr = v.split('_');
            text[keysArr[0]][keysArr[1]][updateType] = value;
        });

        const targets = [
            {
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        text,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /**
     * 更新Cell中属性
     */
    public static updateCell(params: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        updateType: string;
        value: string | number;
        opacityBg?: number;
        fun_name: string;
    }): void {
        const { updateType = '', value = '', opacityBg, fun_name } = params;

        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset: IAsset = AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index });

        const cells = cloneDeep(asset.attribute.cell);
        const texts = cloneDeep(asset.attribute.text);

        if (updateType == 'fontSize') {
            assetManager.setPv_new(2342);
        }
        if (
            updateType === 'fontSize' ||
            updateType === 'fontWeight' ||
            updateType === 'textAlign' ||
            updateType === 'lineHeight' ||
            updateType === 'fontFamily' ||
            updateType === 'letterSpacing' ||
            updateType === 'writingMode' ||
            updateType === 'fontStyle' ||
            updateType === 'textDecoration' ||
            updateType === 'fontColor'
        ) {
            const attrKey = updateType == 'fontColor' ? 'color' : updateType;
            for (const rowKey in texts) {
                //遍历所有text节点 修改其属性
                const row = texts[rowKey];
                // @ts-ignore
                for (const colKey in row) {
                    texts[rowKey][colKey][attrKey] = value;
                }
            }
        } else {
            for (const rowKey in cells) {
                //遍历所有text节点 修改其属性
                const row = texts[rowKey];
                // @ts-ignore
                for (const colKey in row) {
                    if (updateType == 'borderColor') {
                        // @ts-ignore
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        cells[rowKey][colKey] ? (cells[rowKey][colKey].lineColor = [value, value, value, value]) : '';
                    } else if (updateType == 'backgroundColor') {
                        // @ts-ignore
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        cells[rowKey][colKey] ? (cells[rowKey][colKey].background.color = value) : '';
                        // @ts-ignore
                        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                        cells[rowKey][colKey] ? (cells[rowKey][colKey].background.opacityBg = opacityBg) : '';
                    }
                }
            }
        }

        const targets = [
            {
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        text: texts,
                        cell: cells,
                    },
                },
            },
        ];
        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* 更新表格文字 */
    public static updateTextSpecial(params: {
        assetClassName: string;
        assetIndex: number;
        row: number;
        col: number;
        value: string[];
        fun_name: string;
        pageIndex?: number;
    }): void {
        const { fun_name, assetIndex, row, col, value, pageIndex, assetClassName } = params;

        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        let text: IText;
        const pageNow = typeof pageIndex === 'number' && pageIndex >= 0 ? pageIndex : pageInfo.pageNow;
        const _pageAssets = work.pages[pageNow].assets;
        _pageAssets.forEach((v: IAsset) => {
            if (v.meta.className === assetClassName) {
                // @ts-ignore
                text = cloneDeep(v.attribute.text);
            }
        });
        text[row][col].content = value;

        const targets = [
            {
                index: assetIndex,
                className: assetClassName,
                pageIndex: pageNow,
                changes: {
                    attribute: {
                        text,
                    },
                },
            },
        ];
        // @ts-ignore
        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static updateHeightAndRowHeight(params: {
        assetClassName: string;
        row: number;
        col: number;
        tableHeight: number;
        rowSize: number;
        assetIndex: number;
        fun_name: string;
        pageIndex?: number;
    }): void {
        const { fun_name, tableHeight, assetIndex, pageIndex, assetClassName } = params;

        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        let cellSize: ICellSize;

        const pageNow = pageIndex ? pageIndex : pageInfo.pageNow;
        const _pageAssets = work.pages[pageNow].assets;
        _pageAssets.forEach((v: IAsset) => {
            if (v.meta.className === assetClassName) {
                cellSize = v.attribute.cellSize;
            }
        });
        // @ts-ignore
        cellSize['row'][row] = rowSize;

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cellSize,
                        height: tableHeight,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* 改变table的高度 */
    public static updateOutterHeight(params: {
        height: number;
        assetIndex: number;
        fun_name: string;
        pageIndex?: number;
    }): void {
        const { fun_name, assetIndex, height, pageIndex } = params;
        const targets = [
            {
                index: assetIndex,
                pageIndex,
                changes: {
                    attribute: {
                        height,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* *******表格右键操作 ********** */
    public static rightMenuMerge(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        assetIndex: number;
        fun_name: string;
    }): void {
        const { minRow, maxRow, minCol, cell, maxCol, fun_name, assetIndex } = params;
        let row = maxRow;
        while (row >= minRow) {
            let col = maxCol;
            while (col >= minCol) {
                if (row == minRow && col == minCol) {
                    cell[minRow][minCol].merge = { x: maxRow, y: maxCol };
                } else {
                    cell[row][col].merged = { x: minRow, y: minCol };
                }
                col--;
            }
            row--;
        }

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cell,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static rightMenuSplit(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        assetIndex: number;
        fun_name: string;
        pageIndex?: number;
    }): void {
        const { minRow, minCol, cell, fun_name, assetIndex, pageIndex } = params;
        let { maxRow, maxCol } = params;
        if (minRow === maxRow && minCol === maxCol && cell[minRow][minCol].merge) {
            maxRow = cell[minRow][minCol].merge.x;
            maxCol = cell[minRow][minCol].merge.y;
        }
        if (!(minRow === maxRow && minCol === maxCol)) {
            let row = maxRow;
            while (row >= minRow) {
                let col = maxCol;
                while (col >= minCol) {
                    const item = cell[row][col];
                    if (item.merge) {
                        delete item.merge;
                    }
                    if (item.merged) {
                        delete item.merged;
                    }
                    col--;
                }
                row--;
            }
        }

        const targets = [
            {
                index: assetIndex,
                pageIndex,
                changes: {
                    attribute: {
                        cell,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static rightMenuInsertLeft(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        text: IText[][];
        cellSize: ICellSize;
        width: number;
        insertNewColWidth: number;
        assetIndex: number;
        fun_name: string;
        pageIndex?: number;
    }): void {
        const { minCol, width, cell, text, cellSize, insertNewColWidth, fun_name, assetIndex, pageIndex } = params;
        const cloneCells = cloneDeep(cell);
        const cloneText = cloneDeep(text);
        const cloneCellSize = cloneDeep(cellSize);
        const actionCol = minCol; // 操作的基础列
        const targetCol = minCol; // 插入后的目标列
        const mergeInfo: TMergeInfo = [];
        for (const row in cloneCells) {
            for (const col in cloneCells[row]) {
                const item = cloneCells[row][col];
                if (item.merge && !item.merged) {
                    const mergeKey = `${row}-${col}`;
                    const startRow = Number(row);
                    const endRow = item.merge.x;
                    let startCol = Number(col);
                    let endCol = item.merge.y;
                    if (actionCol <= startCol) {
                        startCol = startCol + 1;
                        endCol = endCol + 1;
                    } else if (actionCol <= endCol) {
                        endCol = endCol + 1;
                    }
                    mergeInfo.push({
                        key: mergeKey,
                        startRow,
                        startCol,
                        endRow,
                        endCol,
                    });
                }
            }
        }
        // 增加新列, 单独循环，不能和其他循环一起，避免索引变化产生问题
        for (const row in cloneCells) {
            cloneCells[row].splice(actionCol, 0, cloneDeep(cloneCells[row][actionCol]));
            cloneText[row].splice(actionCol, 0, cloneDeep(cloneText[row][actionCol]));
            cloneText[row][targetCol].content = [];
        }
        //按顺序修改key值 并修改合并点 merge 及 merged
        this.formatTableMerge(cloneCells, mergeInfo);
        cloneCellSize.col.splice(actionCol, 0, cloneCellSize.col[actionCol]);

        const newwTableWidth = width + Math.round(insertNewColWidth);
        const targets = [
            {
                index: assetIndex,
                pageIndex,
                changes: {
                    attribute: {
                        cell: cloneCells,
                        text: cloneText,
                        cellSize: cloneCellSize,
                        width: newwTableWidth
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static rightMenuInsertRight(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        text: IText[][];
        cellSize: ICellSize;
        assetIndex: number;
        width: number;
        insertNewColWidth: number;
        fun_name: string;
        pageIndex?: number;
    }): void {
        const { cell, text, width, insertNewColWidth, cellSize, fun_name, maxCol, assetIndex, pageIndex } = params;
        const cloneCells = cloneDeep(cell);
        const cloneText = cloneDeep(text);
        const cloneCellSize = cloneDeep(cellSize);
        const actionCol = maxCol; // 操作的基础列
        const targetCol = maxCol + 1; // 插入后的目标列
        const mergeInfo: TMergeInfo = [];
        for (const row in cloneCells) {
            for (const col in cloneCells[row]) {
                const item = cloneCells[row][col];
                if (item.merge && !item.merged) {
                    const mergeKey = `${row}-${col}`;
                    const startRow = Number(row);
                    const endRow = item.merge.x;
                    let startCol = Number(col);
                    let endCol = item.merge.y;
                    if (actionCol < startCol) {
                        startCol = startCol + 1;
                        endCol = endCol + 1;
                    } else if (actionCol < endCol) {
                        endCol = endCol + 1;
                    }
                    mergeInfo.push({
                        key: mergeKey,
                        startRow,
                        startCol,
                        endRow,
                        endCol,
                    });
                }
            }
        }
        // 增加新列, 单独循环，不能和其他循环一起，避免索引变化产生问题
        for (const row in cloneCells) {
            cloneCells[row].splice(actionCol + 1, 0, cloneDeep(cloneCells[row][actionCol]));
            cloneText[row].splice(actionCol + 1, 0, cloneDeep(cloneText[row][actionCol]));
            cloneText[row][targetCol].content = [];
        }
        //按顺序修改key值 并修改合并点 merge 及 merged
        this.formatTableMerge(cloneCells, mergeInfo);
        cloneCellSize.col.splice(actionCol + 1, 0, cloneCellSize.col[actionCol]);

        const newwTableWidth = width + Math.round(insertNewColWidth);
        const targets = [
            {
                index: assetIndex,
                pageIndex,
                changes: {
                    attribute: {
                        cell: cloneCells,
                        text: cloneText,
                        cellSize: cloneCellSize,
                        width: newwTableWidth
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static rightMenuInsertTop(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        text: IText[][];
        height: number;
        insertNewRowHeight: number;
        cellSize: ICellSize;
        assetIndex: number;
        fun_name: string;
    }): void {
        const { cell, height, insertNewRowHeight, text, cellSize, fun_name, minRow, assetIndex } = params;
        const cloneCells = cloneDeep(cell);
        const cloneText = cloneDeep(text);
        const cloneCellSize = cloneDeep(cellSize);
        const actionRow = minRow; // 操作的基础行
        const targetRow = minRow; // 插入后的目标行
        const mergeInfo: TMergeInfo = [];
        for (const row in cloneCells) {
            for (const col in cloneCells[row]) {
                const item = cloneCells[row][col];
                if (item.merge && !item.merged) {
                    const mergeKey = `${row}-${col}`;
                    let startRow = Number(row);
                    let endRow = item.merge.x;
                    const startCol = Number(col);
                    const endCol = item.merge.y;
                    if (actionRow <= startRow) {
                        startRow = startRow + 1;
                        endRow = endRow + 1;
                    } else if (actionRow <= endRow) {
                        endRow = endRow + 1;
                    }
                    mergeInfo.push({
                        key: mergeKey,
                        startRow,
                        startCol,
                        endRow,
                        endCol,
                    });
                }
            }
        }
        cloneCells.splice(actionRow, 0, cloneDeep(cloneCells[actionRow]));
        cloneText.splice(actionRow, 0, cloneDeep(cloneText[actionRow]));
        cloneText[targetRow]?.forEach((col) => {
            col.content = [];
        });
        //按顺序修改key值 并修改合并点 merge 及 merged
        this.formatTableMerge(cloneCells, mergeInfo);
        cloneCellSize.row.splice(actionRow, 0, cloneCellSize.row[actionRow]);

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cell: cloneCells,
                        text: cloneText,
                        cellSize: cloneCellSize,
                        height: height + Math.round(insertNewRowHeight)
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static rightMenuInsertBottom(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        text: IText[][];
        height: number;
        insertNewRowHeight: number;
        cellSize: ICellSize;
        assetIndex: number;
        fun_name: string;
    }): void {
        const { cell, height, insertNewRowHeight, text, cellSize, fun_name, maxRow, assetIndex } = params;
        const cloneCells = cloneDeep(cell);
        const cloneText = cloneDeep(text);
        const cloneCellSize = cloneDeep(cellSize);
        const actionRow = maxRow; // 操作的基础行
        const targetRow = maxRow + 1; // 插入后的目标行
        const mergeInfo: TMergeInfo = [];
        for (const row in cloneCells) {
            for (const col in cloneCells[row]) {
                const item = cloneCells[row][col];
                if (item.merge && !item.merged) {
                    const mergeKey = `${row}-${col}`;
                    let startRow = Number(row);
                    let endRow = item.merge.x;
                    const startCol = Number(col);
                    const endCol = item.merge.y;
                    if (actionRow < startRow) {
                        startRow = startRow + 1;
                        endRow = endRow + 1;
                    } else if (actionRow < endRow) {
                        endRow = endRow + 1;
                    }
                    mergeInfo.push({
                        key: mergeKey,
                        startRow,
                        startCol,
                        endRow,
                        endCol,
                    });
                }
            }
        }

        cloneCells.splice(actionRow + 1, 0, cloneDeep(cloneCells[actionRow]));
        cloneText.splice(actionRow + 1, 0, cloneDeep(cloneText[actionRow]));
        cloneText[targetRow]?.forEach((col) => {
            col.content = [];
        });
        //按顺序修改key值 并修改合并点 merge 及 merged
        this.formatTableMerge(cloneCells, mergeInfo);
        cloneCellSize.row.splice(actionRow, 0, cloneCellSize.row[actionRow]);

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cell: cloneCells,
                        text: cloneText,
                        cellSize: cloneCellSize,
                        height: height + Math.round(insertNewRowHeight)
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static rightMenuDeleteRow(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        text: IText[][];
        height: number;
        choosedTdKeys: string[] | undefined;
        deleteRowHeight: number;
        cellSize: ICellSize;
        assetIndex: number;
        fun_name: string;
    }): void {
        const { cell, text,height, choosedTdKeys,deleteRowHeight, cellSize, minRow, fun_name, maxRow, assetIndex } = params;
        const cloneCells = cloneDeep(cell);
        const cloneText = cloneDeep(text);
        const cloneSize = cloneDeep(cellSize);
        const mergeInfo: TMergeInfo = [];
        for (const row in cloneCells) {
            for (const col in cloneCells[row]) {
                const item = cloneCells[row][col];
                if (item.merge && !item.merged) {
                    const mergeKey = `${row}-${col}`;
                    let startRow = Number(row);
                    let endRow = item.merge.x;
                    const startCol = Number(col);
                    const endCol = item.merge.y;
                    if (maxRow < startRow) {
                        startRow = startRow - (maxRow - minRow + 1);
                        endRow = endRow - (maxRow - minRow + 1);
                    } else if (endRow < minRow) {
                        /* do nothing */
                    } else if (minRow <= startRow && endRow <= maxRow) {
                        // 跳过，表示删除合并信息
                        continue;
                    } else if (minRow <= startRow && maxRow < endRow) {
                        startRow = minRow;
                        endRow = endRow - (maxRow - minRow + 1);
                    } else if (startRow < minRow && maxRow <= endRow) {
                        endRow = endRow - (maxRow - minRow + 1);
                    } else if (startRow < minRow && endRow < maxRow) {
                        endRow = minRow - 1;
                    }
                    mergeInfo.push({
                        key: mergeKey,
                        startRow,
                        startCol,
                        endRow,
                        endCol,
                    });
                }
            }
        }
        cloneCells.splice(minRow, maxRow - minRow + 1);
        cloneText.splice(minRow, maxRow - minRow + 1);
        cloneSize.row.splice(minRow, maxRow - minRow + 1);
        this.formatTableMerge(cloneCells, mergeInfo);

        const targets: Parameters<typeof UpdateAsset.updateAssets>[1] = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cell: cloneCells,
                        text: cloneText,
                        cellSize: cloneSize,
                        height: height - Math.round(deleteRowHeight)
                    },
                    choosedTdKeys: (choosedTdKeys && choosedTdKeys.length > 0) && [] 
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static rightMenuDeleteCol(params: {
        minRow: number;
        maxRow: number;
        minCol: number;
        maxCol: number;
        cell: ICell[][];
        text: IText[][];
        cellSize: ICellSize;
        width: number;
        choosedTdKeys: string[] | undefined;
        deleteColWidth: number;
        assetIndex: number;
        fun_name: string;
    }): void {
        const { cell, text, width, choosedTdKeys, deleteColWidth, cellSize, minCol, fun_name, maxCol, assetIndex } = params;
        const cloneCells = cloneDeep(cell);
        const cloneText = cloneDeep(text);
        const cloneSize = cloneDeep(cellSize);
        const mergeInfo: TMergeInfo = [];
        for (const row in cloneCells) {
            for (const col in cloneCells[row]) {
                const item = cloneCells[row][col];
                if (item.merge && !item.merged) {
                    const mergeKey = `${row}-${col}`;
                    const startRow = Number(row);
                    const endRow = item.merge.x;
                    let startCol = Number(col);
                    let endCol = item.merge.y;
                    if (maxCol < startCol) {
                        startCol = startCol - (maxCol - minCol + 1);
                        endCol = endCol - (maxCol - minCol + 1);
                    } else if (endCol < minCol) {
                        /* do nothing */
                    } else if (minCol <= startCol && endCol <= maxCol) {
                        // 跳过，表示删除合并信息
                        continue;
                    } else if (minCol <= startCol && maxCol < endCol) {
                        startCol = minCol;
                        endCol = endCol - (maxCol - minCol + 1);
                    } else if (startCol < minCol && maxCol <= endCol) {
                        endCol = endCol - (maxCol - minCol + 1);
                    } else if (startCol < minCol && endCol < maxCol) {
                        endCol = minCol - 1;
                    }
                    mergeInfo.push({
                        key: mergeKey,
                        startRow,
                        startCol,
                        endRow,
                        endCol,
                    });
                }
            }
        }
        cloneCells.forEach((row) => {
            row.splice(minCol, maxCol - minCol + 1);
        });
        cloneText.forEach((row) => {
            row.splice(minCol, maxCol - minCol + 1);
        });
        cloneSize.col.splice(minCol, maxCol - minCol + 1);
        this.formatTableMerge(cloneCells, mergeInfo);

        const targets: Parameters<typeof UpdateAsset.updateAssets>[1] = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cell: cloneCells,
                        text: cloneText,
                        cellSize: cloneSize,
                        width: width - Math.round(deleteColWidth)
                    },
                    choosedTdKeys: (choosedTdKeys && choosedTdKeys.length > 0) && []
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static updateTdScale(params: {
        direction: string;
        pointIndex: number;
        value: number;
        tableClass: string;
        fun_name: string;
        dynamicMinHeight?: number;
    }): void {
        const { fun_name, direction, pointIndex, dynamicMinHeight } = params;
        let { value } = params;

        const { toolPanel, canvas, work, pageInfo } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = cloneDeep(AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index }));

        value = value / canvas.scale;

        const cellSize = asset.attribute.cellSize;
        const minColWidth = dynamicMinHeight && (direction === 'left' || direction === 'right') ? 
            dynamicMinHeight : this.minTableCellWidth; // 列拖拽时使用动态最小宽度
        const minRowHeight = dynamicMinHeight && (direction === 'top' || direction === 'bottom') ? 
            dynamicMinHeight : this.minTableCellHeight; // 行拖拽时使用动态最小高度
        const oldTableHeight = asset.attribute.height;
        const oldTableWidth = asset.attribute.width;
        
        console.log('AssetTableLogic - 使用的最小高度:', minRowHeight);
        console.log('AssetTableLogic - 使用的最小宽度:', minColWidth);
        
        // 提前检查：计算新高度是否会小于最小值
        if (direction === 'top' || direction === 'bottom') {
            const allSize = cellSize.row.reduce((a, b) => a + b);
            let rowIndex = pointIndex;
            if (pointIndex !== 0 && direction === 'top') {
                rowIndex = pointIndex - 1;
            }
            const oldRowHeight = (cellSize.row[rowIndex] / allSize) * oldTableHeight;
            let adjustedValue = value;
            if (pointIndex === 0 && direction === 'top') {
                adjustedValue *= -1;
            }
            const newRowHeight = oldRowHeight + adjustedValue;
            
            console.log('AssetTableLogic - oldRowHeight:', oldRowHeight);
            console.log('AssetTableLogic - adjustedValue:', adjustedValue); 
            console.log('AssetTableLogic - newRowHeight:', newRowHeight);
            console.log('AssetTableLogic - minRowHeight:', minRowHeight);
            
            // 如果新高度小于最小高度，直接返回，不执行任何更新
            if (newRowHeight < minRowHeight) {
                console.log('AssetTableLogic - 阻止更新：新高度小于动态最小高度');
                return;
            }
        }
        
        // 提前检查：计算新宽度是否会小于最小值
        if (direction === 'left' || direction === 'right') {
            const allSize = cellSize.col.reduce((a, b) => a + b);
            let colIndex = pointIndex;
            if (pointIndex !== 0 && direction === 'left') {
                colIndex = pointIndex - 1;
            }
            const oldColWidth = (cellSize.col[colIndex] / allSize) * oldTableWidth;
            let adjustedValue = value;
            if (pointIndex === 0 && direction === 'left') {
                adjustedValue *= -1;
            }
            const newColWidth = oldColWidth + adjustedValue;
            
            console.log('AssetTableLogic - oldColWidth:', oldColWidth);
            console.log('AssetTableLogic - adjustedValue:', adjustedValue); 
            console.log('AssetTableLogic - newColWidth:', newColWidth);
            console.log('AssetTableLogic - minColWidth:', minColWidth);
            
            // 如果新宽度小于最小宽度，直接返回，不执行任何更新
            if (newColWidth < minColWidth) {
                console.log('AssetTableLogic - 阻止更新：新宽度小于动态最小宽度');
                return;
            }
        }
        
        if (direction === 'top' || direction === 'bottom') {
            const allSize = cellSize.row.reduce((a, b) => a + b);
            if (pointIndex === 0 && direction === 'top') {
                value *= -1;
            }
            let newRowHeight = 0;
            let oldRowHeight = 0;
            let otherRowsHeight = 0;
            let newRowPercent = 0;
            let rowIndex = pointIndex;
            if (pointIndex !== 0 && direction === 'top') {
                rowIndex = pointIndex - 1;
            }
            const oldSize = cellSize.row[rowIndex];
            oldRowHeight = (cellSize.row[rowIndex] / allSize) * oldTableHeight;
            otherRowsHeight = oldTableHeight - oldRowHeight;
            newRowHeight = oldRowHeight + value;
            newRowPercent = (newRowHeight / oldRowHeight) * oldSize;
            if (newRowHeight < minRowHeight) {
                newRowPercent = (minRowHeight / oldRowHeight) * oldSize;
                newRowHeight = minRowHeight;
            }
            cellSize.row[rowIndex] = newRowPercent;
            asset.attribute.height = otherRowsHeight + newRowHeight;
            if (direction === 'top') {
                asset.transform.posY -= newRowHeight - oldRowHeight;
            }
        } else if (direction === 'left' || direction === 'right') {
            const allSize = cellSize.col.reduce((a, b) => a + b);
            if (pointIndex === 0 && direction === 'left') {
                value *= -1;
            }
            let newColWidth = 0;
            let oldColWidth = 0;
            let otherColWidth = 0;
            let newColPercent = 0;
            let colIndex = pointIndex;
            if (pointIndex !== 0 && direction === 'left') {
                colIndex = pointIndex - 1;
            }
            const oldSize = cellSize.col[colIndex];
            oldColWidth = (cellSize.col[colIndex] / allSize) * oldTableWidth;
            otherColWidth = oldTableWidth - oldColWidth;
            newColWidth = oldColWidth + value;
            newColPercent = (newColWidth / oldColWidth) * oldSize;
            if (newColWidth < minColWidth) {
                newColPercent = (minColWidth / oldColWidth) * oldSize;
                newColWidth = minColWidth;
            }
            cellSize.col[colIndex] = newColPercent;
            asset.attribute.width = otherColWidth + newColWidth;
            if (direction === 'left') {
                asset.transform.posX -= newColWidth - oldColWidth;
            }
        }

        const targets = [
            {
                index: toolPanel.asset_index,
                changes: {
                    ...asset,
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static updateTableScale(params: {
        direction: string;
        pointIndex: number;
        value: number;
        tableClass: string;
        fun_name: string;
    }): void {
        const { direction = '', fun_name, value = 0 } = params;
        const { toolPanel, work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (typeof toolPanel.asset_index !== 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = cloneDeep(AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index }));
        if (!asset) {
            return;
        }
        let changes: Partial<IAsset> = {};

        if (direction == 'top') {
            asset.attribute.height -= value;
            asset.transform.posY += value;

            changes = {
                attribute: {
                    height: asset.attribute.height - value,
                },
                transform: {
                    posY: asset.transform.posY + value,
                },
            };
        } else if (direction == 'bottom') {
            asset.attribute.height += value;
            changes = {
                attribute: {
                    height: asset.attribute.height - value,
                },
            };
        } else if (direction == 'left') {
            asset.attribute.width -= value;
            asset.transform.posX += value;

            changes = {
                attribute: {
                    width: asset.attribute.width - value,
                },
                transform: {
                    posX: asset.transform.posX + value,
                },
            };
        } else if (direction == 'right') {
            asset.attribute.width += value;
            changes = {
                attribute: {
                    width: asset.attribute.width + value,
                },
            };
        }

        const targets = [
            {
                index: toolPanel.asset_index,
                changes,
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    public static clearSelectContents(params: {
        choosedTdKeys: string[];
        text: IText[][];
        assetIndex: number;
        fun_name: string;
    }): void {
        const {choosedTdKeys, text, fun_name, assetIndex } = params;
        const cloneText = cloneDeep(text);
        choosedTdKeys.forEach(v => {
            const row = Number(v.split('_')[0]) ;
            const col = Number(v.split('_')[1]) ;
            cloneText[row][col].content = [];
        })
        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        text:cloneText,
                    },
                },
            },
        ];
        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* 设置表格选中的td key值 */
    public static updateSelectKey(params: { tdKeys: string[]; className?: string; fun_name: string }): void {
        const { tdKeys = [], className, fun_name } = params;
        const { toolPanel } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });
        const targets = [
            {
                index: toolPanel.asset_index,
                changes: {
                    choosedTdKeys: tdKeys,
                },
                className,
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* 设置表格边框颜色 */
    public static updateBorderColor(params: {
        tbKeys: string[];
        color: IColor;
        cell: ICell[][];
        assetIndex: number;
        fun_name: string;
    }): void {
        const { tbKeys = [], cell, color, assetIndex, fun_name } = params;

        tbKeys.map((v: string) => {
            const keysArr = v.split('_');
            cell[keysArr[0]][keysArr[1]].lineColor[0] = color;
            cell[keysArr[0]][keysArr[1]].lineColor[1] = color;
            cell[keysArr[0]][keysArr[1]].lineColor[2] = color;
            cell[keysArr[0]][keysArr[1]].lineColor[3] = color;
        });

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cell,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* 设置表格背景颜色 */
    public static updateBgColor(params: {
        tbKeys: string[];
        color: IColor;
        cell: ICell[][];
        opacityBg?: boolean;
        assetIndex: number;
        fun_name: string;
    }): void {
        const { tbKeys = [], assetIndex, fun_name, color, cell, opacityBg = false } = params;
        tbKeys.map((v: string) => {
            const keysArr = v.split('_');
            cell[keysArr[0]][keysArr[1]].background.color = color;
            cell[keysArr[0]][keysArr[1]].background.opacityBg = opacityBg;
        });

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        cell,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* 设置表格文本颜色 */
    public static updateTextColor(params: {
        tbKeys: string[];
        color: IColor;
        text: IText[][];
        assetIndex: number;
        fun_name: string;
    }): void {
        const { tbKeys = [], text, assetIndex, fun_name, color } = params;
        tbKeys.map((v: string) => {
            const keysArr = v.split('_');
            text[keysArr[0]][keysArr[1]].color = color;
        });

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        text,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /* 设置表格透明度 */
    public static updateTextOpacity(params: {
        tbKeys: string[];
        opacity: number;
        text: IText[][];
        assetIndex: number;
        fun_name: string;
    }): void {
        const { tbKeys = [], text, assetIndex, fun_name, opacity } = params;
        tbKeys.map((v: string) => {
            const keysArr = v.split('_');
            text[keysArr[0]][keysArr[1]].opacity = opacity;
        });

        const targets = [
            {
                index: assetIndex,
                changes: {
                    attribute: {
                        text,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets(fun_name, targets);
    }

    /** 复制选中的单元格信息存到 localstorage */
    public static copySelectedTds(tableAsset: IAsset) {
        const { startRow, endRow, startCol, endCol } = this.calcSelectedTds(tableAsset.choosedTdKeys)
        const { cell, text } = tableAsset.attribute;
        const copyTds: {
            cell: ICell[][];
            text: IText[][];
        } = { cell: [], text: [] };
        for (let r = startRow; r <= endRow; r++) {
            const cellArray: ICell[] = [];
            const textArray: IText[] = [];
            for (let c = startCol; c <= endCol; c++) {
                cellArray.push(cloneDeep(cell[r][c]));
                textArray.push(cloneDeep(text[r][c]));
            }
            copyTds.cell.push(cellArray);
            copyTds.text.push(textArray);
        }
        localStorage.setItem('ueCopyTds', JSON.stringify(copyTds));
    }

    /** 剪切选中的单元格信息存到 localstorage */
    public static cutSelectedTds(tableAsset: IAsset, assetIndex: number) {
        const { startRow, endRow, startCol, endCol } = this.calcSelectedTds(tableAsset.choosedTdKeys)
        let { cell, text } = tableAsset.attribute;
        cell = cloneDeep(cell);
        text = cloneDeep(text);
        const copyTds: {
            cell: ICell[][];
            text: IText[][];
        } = { cell: [], text: [] };
        const {
            lineStyle,
            lineColor,
            lineWidth ,
            bgColor ,
            fontColor,
            fontFaimly,
            fontSize,
        } = this.defaultCell
        for (let r = startRow; r <= endRow; r++) {
            const cellArray: ICell[] = [];
            const textArray: IText[] = [];
            for (let c = startCol; c <= endCol; c++) {
                cellArray.push(cloneDeep(cell[r][c]));
                textArray.push(cloneDeep(text[r][c]));
                cell[r][c] = cloneDeep( {
                        lineStyle: [lineStyle,lineStyle, lineStyle, lineStyle],
                        lineColor: [lineColor, lineColor, lineColor, lineColor],
                        lineWidth: [lineWidth, lineWidth, lineWidth, lineWidth],
                        background: {
                            color: bgColor,
                        },
                });
                text[r][c] = cloneDeep({
                    content: [''],
                    alpha: 1.0,
                    horizontalFlip: false,
                    verticalFlip: false,
                    fontSize: fontSize,
                    fontWeight: 400,
                    textAlign: 'center',
                    lineHeight: 1.3,
                    fontFamily: fontFaimly,
                    color: fontColor,
                });
            }
            copyTds.cell.push(cellArray);
            copyTds.text.push(textArray);
        }
        localStorage.setItem('ueCopyTds', JSON.stringify(copyTds));
        const targets = [
            {
                index: assetIndex,
                className: tableAsset.meta.className,
                changes: {
                    attribute: {
                        cell,
                        text,
                    },
                },
            },
        ];

        UpdateAsset.updateAssets('CUT_TABLE_SELECTED_TDS', targets);
    }

    /** 粘贴单元格到选中的单元格 */
    public static pasteSelectedTds(tableAsset: IAsset, assetIndex: number) {
        const string = localStorage.getItem('ueCopyTds');
        let copyTds: {
            cell: ICell[][];
            text: IText[][];
        };
        if (string) {
            copyTds = JSON.parse(string);
            const dataRowLength = copyTds?.cell?.length;
            const dataColLength = copyTds?.cell?.[0]?.length;
            if (!dataRowLength || !dataColLength) {
                return;
            }
            const { startRow, endRow, startCol, endCol } = this.calcSelectedTds(tableAsset.choosedTdKeys)
            let { cell, text } = tableAsset.attribute;
            cell = cloneDeep(cell);
            text = cloneDeep(text);
            for (let r = 0; r < dataRowLength; r++) {
                for (let c = 0; c < dataColLength; c++) {
                    if (cell?.[r + startRow]?.[c + startCol]) {
                        cell[r + startRow][c + startCol] = copyTds.cell[r][c];
                        text[r + startRow][c + startCol] = copyTds.text[r][c];
                    }
                }
            }

            const targets = [
                {
                    index: assetIndex,
                    className: tableAsset.meta.className,
                    changes: {
                        attribute: {
                            cell,
                            text,
                        },
                    },
                },
            ];

            UpdateAsset.updateAssets('PASTE_TABLE_SELECTED_TDS', targets);
        }
    }

    /**
     * 表格拖动列交换位置
     */
    public static drageTableCol(params: {start: number, end: number}) {
        const { start, end } = params;
        // console.log(start, end, 'start, end, Col')
        const { toolPanel, canvas, work, pageInfo } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = cloneDeep(AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index }));
        // console.log(asset, 'assetasset')
        const cellSize = asset.attribute.cellSize;
        const cell = asset.attribute.cell;
        const text = asset.attribute.text;
        [cellSize['col'][end],cellSize['col'][start]] = [cellSize['col'][start],cellSize['col'][end]];
        cell.forEach((item, index) => {
            [item[end],item[start]] = [item[start],item[end]];
        })
        text.forEach((item, index) => {
            [item[end],item[start]] = [item[start],item[end]];
        })
        const targets = [
            {
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        cellSize,
                        cell,
                        text
                    },
                },
            },
        ];
        UpdateAsset.updateAssets('DRAGE_TABLE_COL', targets);
    }

    /**
     * 表格拖动行交换位置
     */
    public static drageTableRow(params: {start: number, end: number}) {
        const { start, end } = params;
        // console.log(start, end, 'start, end, row')
        const { toolPanel, canvas, work, pageInfo } = storeAdapter.getStore<
            typeof storeAdapter.store_names.paintOnCanvas
        >({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        if (typeof toolPanel.asset_index != 'number' || toolPanel.asset_index < 0) {
            return;
        }
        const asset = cloneDeep(AssetHelper.find(work, pageInfo, { index: toolPanel.asset_index }));
        const cellSize = asset.attribute.cellSize;
        const cell = asset.attribute.cell;
        const text = asset.attribute.text;
        [cellSize['row'][end],cellSize['row'][start]] = [cellSize['row'][start],cellSize['row'][end]];
        [cell[end],cell[start]] = [cell[start],cell[end]];
        [text[end],text[start]] = [text[start],text[end]];
        // console.log(asset, 'console.log(asset, asset)')
        const targets = [
            {
                index: toolPanel.asset_index,
                changes: {
                    attribute: {
                        cellSize,
                        cell,
                        text
                    },
                },
            },
        ];
        UpdateAsset.updateAssets('DRAGE_TABLE_ROW', targets);
    }
    /**
     * 格式化表格合并单元格信息, 为了兼容老数据, 使用此 for 循环
     * @param cells - 表格单元格信息
     * @param mergeInfo - 合并单元格信息
     */
    private static formatTableMerge(cells: ICell[][], mergeInfo: TMergeInfo) {
        const rowLength = cells.length;
        for (let row = 0; row < rowLength; row++) {
            const colLength = cells[row].length;
            for (let col = 0; col < colLength; col++) {
                const item = cells[row][col];
                if (item.merge) {
                    item.merge = undefined;
                }
                if (item.merged) {
                    item.merged = undefined;
                }
                if (mergeInfo.length > 0) {
                    mergeInfo.forEach((m) => {
                        if (m.startRow === row && m.startCol === col) {
                            item.merge = {
                                x: m.endRow,
                                y: m.endCol,
                            };
                        } else if (m.startRow <= row && row <= m.endRow && m.startCol <= col && col <= m.endCol) {
                            item.merged = {
                                x: m.startRow,
                                y: m.startCol,
                            };
                        }
                    });
                }
            }
        }
    }

    private static calcSelectedTds(choosedTdKeys: string[]) {
        let startRow = -1;
        let endRow = -1;
        let startCol = -1;
        let endCol = -1;
        choosedTdKeys.forEach((s) => {
            const td = s.split('_');
            startRow = startRow === -1 ? Number.parseInt(td[0]) : Math.min(startRow, Number.parseInt(td[0]));
            endRow = endRow === -1 ? Number.parseInt(td[0]) : Math.max(endRow, Number.parseInt(td[0]));
            startCol = startCol === -1 ? Number.parseInt(td[1]) : Math.min(startCol, Number.parseInt(td[1]));
            endCol = endCol === -1 ? Number.parseInt(td[1]) : Math.max(endCol, Number.parseInt(td[1]));
        });
        return { startRow, endRow, startCol, endCol };
    }

    /**
     * 自动调整表格行高以适应文字换行
     * 当列宽改变导致文字换行时，重新计算每行的最小高度
     */
    public static autoAdjustRowHeights(params: {
        assetClassName: string;
        assetIndex: number;
        fun_name: string;
        pageIndex?: number;
    }): void {
        console.log('autoAdjustRowHeights 开始执行，参数:', params);

        const { assetClassName, assetIndex, fun_name, pageIndex } = params;

        const { work, pageInfo } = storeAdapter.getStore<typeof storeAdapter.store_names.paintOnCanvas>({
            store_name: storeAdapter.store_names.paintOnCanvas,
        });

        const pageNow = typeof pageIndex === 'number' && pageIndex >= 0 ? pageIndex : pageInfo.pageNow;
        const _pageAssets = work.pages[pageNow].assets;

        console.log('页面资源数量:', _pageAssets.length, '目标className:', assetClassName);

        let cellSize: ICellSize;
        let text: any[][];
        let cell: ICell[][];
        let width: number;
        let height: number;

        _pageAssets.forEach((v: IAsset, index: number) => {
            console.log(`资源 ${index}:`, {
                className: v.meta.className,
                type: v.meta.type,
                index: v.meta.index
            });

            if (v.meta.className === assetClassName) {
                console.log('找到匹配的表格资源:', v.meta.className);
                cellSize = cloneDeep(v.attribute.cellSize);
                text = v.attribute.text as any[][];
                cell = v.attribute.cell;
                width = v.attribute.width;
                height = v.attribute.height;
            }
        });

        if (!cellSize || !text || !cell) {
            console.warn('未找到目标表格资源或数据不完整', {
                assetClassName,
                cellSize: !!cellSize,
                text: !!text,
                cell: !!cell
            });
            return;
        }

        console.log('成功找到表格数据，开始计算行高');

        // 计算当前的行高
        const totalRowSize = cellSize.row.reduce((a, b) => a + b, 0);
        const baseRow = height / totalRowSize;
        const currentRowHeights = cellSize.row.map((r) => r * baseRow);

        let hasChanges = false;
        const newCellSizeRow = [...cellSize.row];

        // 遍历每一行，计算该行所需的最小高度
        for (let rowIndex = 0; rowIndex < cell.length; rowIndex++) {
            let maxRequiredHeight = this.minTableCellHeight;

            // 遍历该行的每一列，计算文本所需的高度
            for (let colIndex = 0; colIndex < cell[rowIndex].length; colIndex++) {
                const textData = text[rowIndex]?.[colIndex];
                if (!textData) continue;

                // 处理不同类型的文本数据
                let fontSize = 16;
                let lineHeight = 1.2;
                let content: any[] = [];

                if (typeof textData === 'object' && textData !== null) {
                    fontSize = textData.fontSize || 16;
                    lineHeight = textData.lineHeight || 1.2;
                    content = textData.content || [];
                } else if (typeof textData === 'string') {
                    content = [textData];
                } else if (Array.isArray(textData)) {
                    content = textData;
                }

                if (!content || content.length === 0) continue;

                // 计算文本行数（参考getMinHeightFromSelection的正确逻辑）
                let lineCount = Math.max(1, content.length);
                content.forEach((line: any) => {
                    if (typeof line === 'string') {
                        // 计算每行中的换行符
                        const additionalLines = (line.match(/\n/g) || []).length;
                        lineCount += additionalLines;
                    }
                });

                console.log(`行${rowIndex}列${colIndex}文本分析:`, {
                    content数组: content,
                    content长度: content.length,
                    字体大小: fontSize,
                    行高倍数: lineHeight,
                    计算行数: lineCount
                });

                // 估算文本高度
                let lineHeightPx: number;
                if (typeof lineHeight === 'number' && lineHeight < 10) {
                    // 无单位数字，乘以fontSize
                    lineHeightPx = fontSize * lineHeight;
                } else {
                    // 已经是px值或其他单位
                    lineHeightPx = Number(String(lineHeight).replace(/[^\d.]/g, '')) || fontSize * 1.2;
                }

                const estimatedTextHeight = lineHeightPx * lineCount;
                const paddingTotal = 10; // 上下padding各5px
                const requiredCellHeight = estimatedTextHeight + paddingTotal;

                console.log(`单元格高度计算: 行高${lineHeightPx.toFixed(1)} × ${lineCount}行 + 内边距${paddingTotal} = ${requiredCellHeight.toFixed(1)}`);

                maxRequiredHeight = Math.max(maxRequiredHeight, requiredCellHeight);
            }

            // 检查当前行高是否足够
            const currentRowHeight = currentRowHeights[rowIndex];

            console.log(`第${rowIndex}行高度检查:`, {
                当前高度: currentRowHeight.toFixed(1),
                需要高度: maxRequiredHeight.toFixed(1),
                差值: (maxRequiredHeight - currentRowHeight).toFixed(1),
                列宽: cellSize.col.map(w => w.toFixed(1))
            });

            if (maxRequiredHeight > currentRowHeight + 2) { // 添加2px容差，避免微小差异导致的频繁调整
                // 需要调整行高
                const heightRatio = maxRequiredHeight / currentRowHeight;
                newCellSizeRow[rowIndex] = cellSize.row[rowIndex] * heightRatio;
                hasChanges = true;

                console.log(`行 ${rowIndex} 需要调整高度: ${currentRowHeight.toFixed(1)} -> ${maxRequiredHeight.toFixed(1)}`);
            } else {
                console.log(`第${rowIndex}行高度无需调整（差值小于2px容差）`);
            }
        }

        // 如果有变化，更新表格
        if (hasChanges) {
            // 重新计算总高度
            const newTotalRowSize = newCellSizeRow.reduce((a, b) => a + b, 0);
            const newHeight = (newTotalRowSize / totalRowSize) * height;

            const targets = [
                {
                    index: assetIndex,
                    pageIndex,
                    changes: {
                        attribute: {
                            cellSize: {
                                ...cellSize,
                                row: newCellSizeRow,
                            },
                            height: newHeight,
                        },
                    },
                },
            ];

            UpdateAsset.updateAssets(fun_name, targets);
            console.log('表格行高已自动调整以适应文字换行，新高度:', newHeight.toFixed(1));
        } else {
            console.log('表格行高无需调整');
        }
    }
}
